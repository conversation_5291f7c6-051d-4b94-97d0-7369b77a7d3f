<template>
  <div class="process-form">
    <n-card :bordered="false" class="shadow-sm">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium">{{ title }}</h3>
          <n-tag :type="statusType" size="small" round>
            {{ statusText }}
          </n-tag>
        </div>
      </template>

      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="medium"
        class="max-w-3xl"
      >
        <n-form-item label="阶段名称" path="stageName">
          <n-input v-model:value="formData.stageName" placeholder="请输入阶段名称" disabled />
        </n-form-item>
        
        <n-form-item label="开始时间" path="startTime">
          <n-date-picker
            v-model:formatted-value="formData.startTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            clearable
            style="width: 100%"
            :is-date-disabled="disablePreviousDate"
          />
        </n-form-item>
        
        <n-form-item label="结束时间" path="endTime">
          <n-date-picker
            v-model:formatted-value="formData.endTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            clearable
            style="width: 100%"
            :is-date-disabled="disablePreviousDate"
          />
        </n-form-item>
        
        <n-form-item label="进度" path="progress">
          <n-input-number
            v-model:value="formData.progress"
            :min="0"
            :max="100"
            :step="5"
            style="width: 100%"
          >
            <template #suffix>%</template>
          </n-input-number>
        </n-form-item>
        
        <n-form-item label="负责人" path="owner">
          <n-input v-model:value="formData.owner" placeholder="请输入负责人" />
        </n-form-item>
        
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            :autosize="{
              minRows: 3,
              maxRows: 5
            }"
            placeholder="请填写阶段描述..."
          />
        </n-form-item>
        
        <n-form-item label="附件" path="attachments">
          <n-upload
            v-model:file-list="formData.attachments"
            multiple
            directory-dnd
            :max="5"
            :on-before-upload="beforeUpload"
            :on-remove="handleRemove"
            list-type="image-card"
            class="w-full"
          >
            <n-upload-dragger class="w-full">
              <div class="py-4">
                <n-icon size="48" :depth="3" class="mb-2">
                  <CloudUploadOutline />
                </n-icon>
                <n-text>点击或拖拽文件到此处上传</n-text>
                <n-p depth="3" style="margin: 8px 0 0 0">
                  支持单个文件不超过 10MB
                </n-p>
              </div>
            </n-upload-dragger>
          </n-upload>
        </n-form-item>
        
        <div class="flex justify-end gap-3 mt-6">
          <n-button @click="handleReset">重置</n-button>
          <n-button type="primary" :loading="submitting" @click="handleSubmit">
            提交
          </n-button>
        </div>
      </n-form>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  NForm, 
  NFormItem, 
  NInput, 
  NButton, 
  NUpload, 
  NUploadDragger, 
  NIcon, 
  NText, 
  NP,
  NDatePicker,
  NInputNumber,
  useMessage,
  type FormRules,
  type UploadFileInfo
} from 'naive-ui'
import { CloudUploadOutline } from '@vicons/ionicons5'

const props = defineProps<{
  stage: {
    label: string
    status: string
    [key: string]: any
  }
}>()

const emit = defineEmits(['submit', 'cancel'])

const message = useMessage()
const formRef = ref()
const submitting = ref(false)

const formData = ref({
  stageName: props.stage.label,
  startTime: null as string | null,
  endTime: null as string | null,
  progress: 0,
  owner: '',
  description: '',
  attachments: [] as UploadFileInfo[]
})

const rules: FormRules = {
  startTime: {
    required: true,
    message: '请选择开始时间',
    trigger: ['blur', 'change']
  },
  endTime: {
    required: true,
    message: '请选择结束时间',
    trigger: ['blur', 'change']
  },
  owner: {
    required: true,
    message: '请输入负责人',
    trigger: 'blur'
  }
}

const statusMap = {
  wait: { type: 'default', text: '未开始' },
  process: { type: 'info', text: '进行中' },
  finish: { type: 'success', text: '已完成' },
  error: { type: 'error', text: '异常' },
  warning: { type: 'warning', text: '警告' }
}

const statusType = computed(() => statusMap[props.stage.status || 'wait'].type)
const statusText = computed(() => statusMap[props.stage.status || 'wait'].text)
const title = computed(() => `${props.stage.label} - 表单`)

const disablePreviousDate = (timestamp: number) => {
  return timestamp < Date.now() - 86400000
}

const beforeUpload = (data: { file: UploadFileInfo }) => {
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (data.file.file?.size && data.file.file.size > maxSize) {
    message.error('文件大小不能超过10MB')
    return false
  }
  return true
}

const handleRemove = (options: { file: UploadFileInfo, fileList: UploadFileInfo[] }) => {
  formData.value.attachments = options.fileList
}

const handleSubmit = (e: MouseEvent) => {
  e.preventDefault()
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      submitting.value = true
      // 模拟API调用
      setTimeout(() => {
        message.success('提交成功')
        submitting.value = false
        emit('submit', formData.value)
      }, 1000)
    } else {
      message.error('请填写完整信息')
    }
  })
}

const handleReset = () => {
  formRef.value?.restoreValidation()
  message.info('已重置表单')
}
</script>

<style scoped>
.process-form {
  @apply w-full h-full;
}

:deep(.n-upload-trigger) {
  width: 100%;
}

:deep(.n-upload-file-list) {
  margin-top: 8px;
}

:deep(.n-upload-dragger) {
  padding: 20px 0;
}
</style>
