<script setup lang="ts">
import { computed } from 'vue'
import type { Task } from '@/types/task'
import { KANBAN_COLUMNS, getIssueTypeInfo, getPriorityInfo, formatDate } from '@/types/task'

interface Props {
  tasks: Task[]
  loading?: boolean
}

interface Emits {
  (e: 'edit', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'view', task: Task): void
  (e: 'statusChange', task: Task, newStatus: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 按状态分组的任务
const tasksByStatus = computed(() => {
  const grouped: Record<string, Task[]> = {}
  KANBAN_COLUMNS.forEach(col => {
    grouped[col.status] = props.tasks.filter(task => task.status === col.status)
  })
  return grouped
})

// 事件处理
const handleEdit = (task: Task) => emit('edit', task)
const handleDelete = (task: Task) => emit('delete', task)
const handleView = (task: Task) => emit('view', task)
const handleStatusChange = (task: Task, newStatus: string) => emit('statusChange', task, newStatus)

// 拖拽处理（简化版，实际项目中可以使用 vue-draggable 等库）
const handleDragStart = (event: DragEvent, task: Task) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', JSON.stringify(task))
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDrop = (event: DragEvent, newStatus: string) => {
  event.preventDefault()
  if (event.dataTransfer) {
    const taskData = event.dataTransfer.getData('text/plain')
    if (taskData) {
      const task = JSON.parse(taskData) as Task
      if (task.status !== newStatus) {
        handleStatusChange(task, newStatus)
      }
    }
  }
}
</script>

<template>
  <div class="kanban-view">
    <div v-if="tasks.length === 0" class="empty-state">
      <n-empty description="暂无任务" />
    </div>
    
    <div v-else class="kanban-columns">
      <div
        v-for="column in KANBAN_COLUMNS"
        :key="column.key"
        class="kanban-column"
        @dragover="handleDragOver"
        @drop="handleDrop($event, column.status)"
      >
        <div class="column-header" :style="{ borderTopColor: column.color }">
          <h4 class="column-title">{{ column.title }}</h4>
          <span class="task-count" :style="{ backgroundColor: column.color }">
            {{ tasksByStatus[column.status]?.length || 0 }}
          </span>
        </div>
        
        <div class="column-content">
          <div
            v-for="task in tasksByStatus[column.status]"
            :key="`kanban-${task.id}`"
            class="kanban-task"
            draggable="true"
            @dragstart="handleDragStart($event, task)"
            @click="handleView(task)"
          >
            <div class="kanban-task-header">
              <div class="task-title">{{ task.title }}</div>
              <n-tag
                :type="getIssueTypeInfo(task.issueType).type"
                size="small"
              >
                {{ getIssueTypeInfo(task.issueType).text }}
              </n-tag>
            </div>

            <div v-if="task.description" class="task-description">
              {{ task.description }}
            </div>

            <div class="kanban-task-meta">
              <div class="assignee">
                <n-avatar :size="20" :src="task.assigneeAvatar">
                  {{ task.assignee.charAt(0) }}
                </n-avatar>
                <span class="assignee-name">{{ task.assignee }}</span>
              </div>
              <div class="reporter">
                <span class="reporter-label">报告:</span>
                <span class="reporter-name">{{ task.reporter }}</span>
              </div>
            </div>

            <div class="kanban-task-time">
              <div class="time-info">
                <span class="time-label">计划:</span>
                <span class="time-range">{{ formatDate(task.plannedStartTime) }} - {{ formatDate(task.plannedEndTime) }}</span>
              </div>
              <div v-if="task.duration" class="duration-info">
                <span class="duration-label">时长:</span>
                <span class="duration">{{ task.duration }}h</span>
              </div>
            </div>

            <div class="kanban-task-priority">
              <n-tag
                :type="getPriorityInfo(task.priority).type"
                size="small"
              >
                {{ getPriorityInfo(task.priority).text }}
              </n-tag>
            </div>
            

            
            <div class="kanban-task-actions">
              <n-button size="tiny" text @click.stop="handleEdit(task)">
                编辑
              </n-button>
              <n-button size="tiny" text type="error" @click.stop="handleDelete(task)">
                删除
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.kanban-view {
  flex: 1;
  overflow: hidden;
  padding: 16px 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.kanban-columns {
  display: flex;
  gap: 16px;
  height: 100%;
  overflow-x: auto;
  padding: 0 16px;
}

.kanban-column {
  flex: 0 0 280px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
  border-top: 4px solid transparent;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  border-top: 4px solid;
}

.column-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.task-count {
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

.column-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.kanban-task {
  background: white;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kanban-task:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  transform: translateY(-1px);
}

.kanban-task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.task-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.task-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.kanban-task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.assignee {
  display: flex;
  align-items: center;
  gap: 6px;
}

.assignee-name {
  font-size: 12px;
  color: #666;
}

.reporter {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.reporter-label {
  color: #999;
}

.reporter-name {
  color: #666;
}

.kanban-task-time {
  margin-bottom: 8px;
}

.time-info, .duration-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  margin-bottom: 4px;
}

.time-label, .duration-label {
  color: #999;
  min-width: 30px;
}

.time-range, .duration {
  color: #666;
}

.kanban-task-priority {
  margin-bottom: 8px;
}

.kanban-task-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.kanban-task:hover .kanban-task-actions {
  opacity: 1;
}
</style>
