<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  NButton,
  NBreadcrumb,
  NBreadcrumbItem,
  NTag,
  NTabs,
  NTabPane,
  NSpace,
  NAvatar,
} from 'naive-ui';
import { ArrowLeftOutlined } from '@vicons/antd';

const route = useRoute();
const router = useRouter();

// 项目信息接口
interface ProjectInfo {
  id: string | string[];
  name: string;
  version: string;
  description: string;
  status: string;
  progress: number;
  startDate: string;
  endDate: string;
  manager: string;
}

// 任务状态类型
type TaskStatus = '未开始' | '进行中' | '已完成' | '已延期' | '已取消';

// 任务接口
interface Task {
  id: number;
  name: string;
  assignee: string;
  status: TaskStatus;
  priority: string;
  dueDate: string;
  avatar?: string;
}

// 项目信息
const projectInfo = ref<ProjectInfo>({
  id: route.params.id,
  name: '项目名称',
  version: '1.0.0',
  description: '项目描述',
  status: '进行中',
  progress: 65,
  startDate: '2023-01-01',
  endDate: '2023-12-31',
  manager: '张三',
});

// 任务数据
const taskList = ref<Task[]>([
  {
    id: 1,
    name: '项目初始化',
    assignee: '张三',
    status: '已完成',
    priority: '高',
    dueDate: '2023-01-15',
    avatar: '',
  },
  {
    id: 2,
    name: '需求分析',
    assignee: '李四',
    status: '进行中',
    priority: '中',
    dueDate: '2023-02-15',
    avatar: '',
  },
  {
    id: 3,
    name: 'UI设计',
    assignee: '王五',
    status: '未开始',
    priority: '中',
    dueDate: '2023-03-01',
    avatar: '',
  },
  {
    id: 4,
    name: '后端开发',
    assignee: '赵六',
    status: '进行中',
    priority: '高',
    dueDate: '2023-02-01',
    avatar: '',
  },
  {
    id: 5,
    name: '前端开发',
    assignee: '钱七',
    status: '未开始',
    priority: '中',
    dueDate: '2023-03-15',
    avatar: '',
  },
  {
    id: 6,
    name: '测试',
    assignee: '孙八',
    status: '未开始',
    priority: '低',
    dueDate: '2023-04-01',
    avatar: '',
  },
  {
    id: 7,
    name: '部署上线',
    assignee: '周九',
    status: '未开始',
    priority: '高',
    dueDate: '2023-04-15',
    avatar: '',
  },
]);

// 任务统计
const taskStats = computed(() => {
  const total = taskList.value.length;
  const completed = taskList.value.filter((t) => t.status === '已完成').length;
  const inProgress = taskList.value.filter((t) => t.status === '进行中').length;
  const notStarted = taskList.value.filter((t) => t.status === '未开始').length;
  const overdue = taskList.value.filter((t) => t.status === '已延期').length;

  return {
    total,
    completed,
    inProgress,
    notStarted,
    overdue,
    completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
  };
});

// 标签页配置
const tabs = [
  { name: 'overview', label: '概览' },
  { name: 'process', label: '流程' },
  { name: 'task', label: '任务' },
  { name: 'document', label: '文档' },
];

// 从路由名中提取标签页名称
const getTabNameFromRoute = (routeName: string | symbol | undefined | null): string => {
  if (!routeName) return 'overview';
  const name = routeName.toString();
  if (!name.startsWith('ProjectDetail')) return 'overview';
  // 将 'ProjectDetailOverview' 转为 'overview', 'ProjectDetailTask' 转为 'task' 等
  return name.replace('ProjectDetail', '').toLowerCase();
};

// 当前激活的标签页
const activeTab = ref(getTabNameFromRoute(route.name));

// 监听路由变化更新激活的标签页
watch(
  () => route.name,
  (newName) => {
    activeTab.value = getTabNameFromRoute(newName);
  },
  { immediate: true },
);

// 处理标签页切换
const handleTabChange = (tab: string) => {
  activeTab.value = tab;
  router.push({
    name: `ProjectDetail${tab.charAt(0).toUpperCase() + tab.slice(1)}`,
    params: { id: route.params.id },
  });
};

// 处理返回
const handleBack = () => {
  router.push({ name: 'ProjectOverview' });
};
</script>

<template>
  <div class="project-detail-container">
    <div class="project-header">
      <div class="header-content">
        <div class="header-left">
          <n-button text @click="handleBack" class="back-btn">
            <template #icon>
              <n-icon><arrow-left-outlined /></n-icon>
            </template>
            返回
          </n-button>
          <h2 class="project-title">{{ projectInfo.name }}</h2>
          <div class="project-tags">
            <n-tag :bordered="false" type="info" size="small" class="project-tag">
              项目编号：{{ projectInfo.id || '--' }}
            </n-tag>
            <n-tag 
              :bordered="false" 
              :type="projectInfo.status === '进行中' ? 'primary' : 'default'" 
              size="small"
              class="project-tag"
            >
              {{ projectInfo.status }}
            </n-tag>
          </div>
        </div>
        
        <n-tabs
          v-model:value="activeTab"
          type="line"
          animated
          @update:value="handleTabChange"
          class="header-tabs"
        >
          <n-tab
            v-for="tab in tabs"
            :key="tab.name"
            :name="tab.name"
            :tab="tab.label"
          />
        </n-tabs>
      </div>
    </div>

    <div class="page-content">

      <!-- 子路由视图 -->
      <router-view 
        :project-info="projectInfo" 
        :task-stats="taskStats"
        :task-list="taskList"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';
/* 项目详情容器 */
.project-detail-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  overflow: auto;
  background-color: @page-bg-color;
  margin: 0;
  padding: 0;
}

/* 头部样式 */
.project-header {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
}

.project-header .header-content {
  width: 100%;
  margin: 0;
  padding: 0 1.5rem;
}

.project-header .header-left {
  display: flex;
  align-items: center;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.project-header .back-btn {
  margin-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #4b5563;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.project-header .back-btn:hover {
  color: #2563eb;
}

.project-header .project-title {
  margin: 0;
  margin-right: 1.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
  color: #111827;
}

.project-header .project-tags {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-header .project-tag {
  margin: 0;
}

/* 标签页样式 */
.project-header .header-tabs :deep(.n-tabs-nav) {
  margin: 0;
  padding: 0;
}

.project-header .header-tabs :deep(.n-tabs-nav) .n-tabs-tab {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s;
  cursor: pointer;
}

.project-header .header-tabs :deep(.n-tabs-nav) .n-tabs-tab:hover {
  color: #2563eb;
  background-color: #f9fafb;
}

.project-header .header-tabs :deep(.n-tabs-nav) .n-tabs-tab.n-tabs-tab--active {
  font-weight: 500;
  color: #2563eb;
}

.project-header .header-tabs :deep(.n-tabs-nav) .n-tabs-tab.n-tabs-tab--active .n-tabs-tab__label::after {
  background-color: #2563eb;
}

/* 页面内容区域 */
.project-content {
  flex: 1;
  padding: 1.5rem;
  width: 100%;
  margin: 0;
  border-radius: 4px;
  margin-top: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  overflow: auto;
}
</style>
