<template>
  <div class="flex flex-1 overflow-hidden bg-gray-50 dark:bg-gray-900 p-4">
    <!-- 左侧流程菜单 -->
    <div class="w-80 bg-white dark:bg-gray-800 border-r border-gray-100 dark:border-gray-700 flex flex-col h-full">
      <!-- 主要步骤 -->
      <div class="border-b border-gray-100 dark:border-gray-700">
        <div class="p-4">
          <div class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400">
            <n-icon :component="DeploymentUnitOutlined" class="mr-2 text-blue-500" />
            <span>主要步骤</span>
          </div>
        </div>
        <div class="px-3 pb-4">
          <div v-for="item in mainProcessOptions" 
               :key="item.key"
               @click="handleMenuSelect(item.key)"
               :class="[
                 'relative mb-2 p-3 rounded-lg cursor-pointer transition-all',
                 activeKey === item.key 
                   ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800/50' 
                   : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-transparent'
               ]">
            <ProcessItem :item="item" :active="activeKey === item.key" />
          </div>
        </div>
      </div>
      
      <!-- 其他步骤 -->
      <div class="flex-1 overflow-hidden flex flex-col">
        <div class="p-4 border-b border-gray-100 dark:border-gray-700">
          <div class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400">
            <n-icon :component="UnorderedListOutlined" class="mr-2 text-blue-500" />
            <span>其他步骤</span>
          </div>
        </div>
        <n-scrollbar class="flex-1">
          <div class="p-3">
            <div v-for="item in secondaryProcessOptions" 
                 :key="item.key"
                 @click="handleMenuSelect(item.key)"
                 :class="[
                   'relative mb-2 p-3 rounded-lg cursor-pointer transition-all',
                   activeKey === item.key 
                     ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800/50' 
                     : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-transparent'
                 ]">
              <ProcessItem :item="item" :active="activeKey === item.key" />
            </div>
          </div>
        </n-scrollbar>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900 pl-4 pr-4">
      <transition name="fade" mode="out-in">
        <div v-if="activeStage" class="h-full">
          <ProcessForm 
            :key="activeKey"
            :stage="activeStage"
            @submit="handleFormSubmit"
          />
        </div>
        <n-empty v-else description="请从左侧选择一个流程阶段" class="h-full flex flex-col items-center justify-center">
          <template #icon>
            <n-icon :size="60" class="text-gray-300">
              <SelectOutlined />
            </n-icon>
          </template>
        </n-empty>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent, h, defineComponent, type PropType, onMounted } from 'vue'
import { 
  NCard, 
  NIcon, 
  NTag, 
  NScrollbar,
  NEmpty
} from 'naive-ui'
import { 
  CodeOutlined, 
  ExperimentOutlined, 
  RocketOutlined, 
  CheckCircleOutlined,
  SafetyOutlined,
  ScheduleOutlined,
  DeploymentUnitOutlined,
  UnorderedListOutlined,
  ClockCircleOutlined,
  SelectOutlined
} from '@vicons/antd'

// 导入表单组件
import ProcessForm from '../components/process/ProcessForm.vue'

// 导入流程项组件
const ProcessItem = defineComponent({
  name: 'ProcessItem',
  props: {
    item: {
      type: Object as PropType<ProcessStage>,
      required: true
    },
    active: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const statusColors = {
      wait: 'gray',
      process: 'blue',
      finish: 'green',
      error: 'red',
      warning: 'orange'
    }

    const statusTextMap = {
      wait: '待开始',
      process: '进行中',
      finish: '已完成',
      error: '异常',
      warning: '警告'
    }

    const formatDate = (timestamp: number, format: string) => {
      const date = new Date(timestamp)
      const map: Record<string, any> = {
        'M+': date.getMonth() + 1,
        'D+': date.getDate(),
        'H+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
      }
      
      if (/(Y+)/.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
      }
      
      for (const k in map) {
        if (new RegExp(`(${k})`).test(format)) {
          format = format.replace(RegExp.$1, 
            RegExp.$1.length === 1 ? map[k] : ('00' + map[k]).substr(('' + map[k]).length))
        }
      }
      
      return format
    }

    return () => h('div', {
      class: 'process-item'
    }, [
      // 顶部行：标题和状态
      h('div', { class: 'flex items-center justify-between mb-1' }, [
        h('div', { class: 'flex items-center' }, [
          h(NIcon, { 
            component: props.item.icon, 
            class: 'mr-2 text-blue-500',
            size: 18
          }),
          h('span', { class: 'font-medium text-gray-900 dark:text-gray-100' }, props.item.label)
        ]),
        h(NTag, {
          size: 'small',
          type: statusColors[props.item.status || 'wait'],
          round: true,
          class: 'ml-2'
        }, { default: () => statusTextMap[props.item.status || 'wait'] })
      ]),
      
      // 描述
      props.item.description && h('div', { 
        class: 'text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-1'
      }, props.item.description),
      
      // 时间进度
      h('div', { class: 'flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-2' }, [
        h('div', { class: 'flex items-center' }, [
          h(NIcon, { component: ClockCircleOutlined, class: 'mr-1', size: 12 }),
          `计划: ${props.item.planStartTime ? formatDate(props.item.planStartTime, 'MM-DD') : '--'} ~ ${props.item.planEndTime ? formatDate(props.item.planEndTime, 'MM-DD') : '--'}`
        ]),
        h('span', {}, props.item.duration)
      ]),
      
      // 进度条
      h('div', { class: 'mb-1' }, [
        h('div', { class: 'flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1' }, [
          h('span', {}, '进度'),
          h('span', {}, `${props.item.progress}%`)
        ]),
        h('div', { 
          class: 'h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden',
        }, [
          h('div', {
            class: 'h-full rounded-full transition-all duration-500',
            class: {
              'bg-blue-500': props.item.status === 'process',
              'bg-green-500': props.item.status === 'finish',
              'bg-red-500': props.item.status === 'error',
              'bg-orange-500': props.item.status === 'warning',
              'bg-gray-300': props.item.status === 'wait',
            },
            style: { width: `${props.item.progress}%` }
          })
        ])
      ])
    ])
  }
})

// 定义流程阶段类型
type ProcessStage = {
  label: string
  key: string
  icon?: any
  component?: string
  status?: 'wait' | 'process' | 'finish' | 'error' | 'warning'
  startTime?: number | null
  endTime?: number | null
  planStartTime?: number | null
  planEndTime?: number | null
  duration?: string
  progress?: number
  description?: string
}

// 当前选中的菜单项
const activeKey = ref<string>('dev')
// 当前激活的标签页
const activeTab = ref<string>('form')
// 当前激活的阶段
const activeStage = ref<ProcessStage | null>(null)

// 主要流程阶段选项
const mainProcessOptions = ref<ProcessStage[]>([
  { 
    label: '开发阶段', 
    key: 'dev',
    icon: CodeOutlined,
    status: 'process',
    description: '系统功能开发和单元测试',
    progress: 65,
    duration: '3天',
    planStartTime: Date.now() - 3 * 24 * 60 * 60 * 1000,
    planEndTime: Date.now() + 4 * 24 * 60 * 60 * 1000,
    startTime: Date.now() - 2 * 24 * 60 * 60 * 1000,
    endTime: null
  },
  { 
    label: '测试阶段', 
    key: 'test',
    icon: ExperimentOutlined,
    status: 'wait',
    description: '系统集成测试和用户验收测试',
    progress: 0,
    duration: '5天',
    planStartTime: Date.now() + 5 * 24 * 60 * 60 * 1000,
    planEndTime: Date.now() + 10 * 24 * 60 * 60 * 1000,
    startTime: null,
    endTime: null
  },
  { 
    label: '上线阶段', 
    key: 'deploy',
    icon: RocketOutlined,
    status: 'wait',
    description: '系统部署和上线',
    progress: 0,
    duration: '2天',
    planStartTime: Date.now() + 11 * 24 * 60 * 60 * 1000,
    planEndTime: Date.now() + 13 * 24 * 60 * 60 * 1000,
    startTime: null,
    endTime: null
  },
  { 
    label: '验收阶段', 
    key: 'acceptance',
    icon: CheckCircleOutlined,
    status: 'wait',
    description: '项目验收和交付',
    progress: 0,
    duration: '3天',
    planStartTime: Date.now() + 14 * 24 * 60 * 60 * 1000,
    planEndTime: Date.now() + 17 * 24 * 60 * 60 * 1000,
    startTime: null,
    endTime: null
  }
])

// 非主要流程阶段选项
const secondaryProcessOptions = ref<ProcessStage[]>([
  { 
    label: '安全测试', 
    key: 'security',
    icon: SafetyOutlined,
    status: 'wait',
    description: '系统安全测试和漏洞修复',
    progress: 0,
    duration: '2天',
    planStartTime: Date.now() + 5 * 24 * 60 * 60 * 1000,
    planEndTime: Date.now() + 7 * 24 * 60 * 60 * 1000,
    startTime: null,
    endTime: null
  },
  { 
    label: '项目计划', 
    key: 'plan',
    icon: ScheduleOutlined,
    status: 'finish',
    description: '项目计划和需求分析',
    progress: 100,
    duration: '5天',
    planStartTime: Date.now() - 10 * 24 * 60 * 60 * 1000,
    planEndTime: Date.now() - 5 * 24 * 60 * 60 * 1000,
    startTime: Date.now() - 10 * 24 * 60 * 60 * 1000,
    endTime: Date.now() - 5 * 24 * 60 * 60 * 1000
  }
])

// 所有阶段
const allStages = computed(() => [
  ...mainProcessOptions.value,
  ...secondaryProcessOptions.value
])

// 状态颜色映射
const statusMap = {
  wait: 'default',
  process: 'primary',
  finish: 'success',
  error: 'error',
  warning: 'warning'
}

// 状态文本映射
const statusTextMap = {
  wait: '待开始',
  process: '进行中',
  finish: '已完成',
  error: '异常',
  warning: '警告'
}

// 表单标签页
const formTabs = [
  { label: '表单', key: 'form' },
  { label: '历史记录', key: 'history' },
  { label: '操作日志', key: 'logs' }
]

// 处理表单提交
const handleFormSubmit = (formData: any) => {
  console.log('Form submitted:', formData)
  // 这里可以添加提交逻辑，比如调用API保存数据
  // 更新阶段状态
  if (activeStage.value) {
    const stage = allStages.value.find(s => s.key === activeStage.value?.key)
    if (stage) {
      stage.status = 'finish'
      stage.progress = 100
      stage.startTime = formData.startTime ? new Date(formData.startTime).getTime() : null
      stage.endTime = formData.endTime ? new Date(formData.endTime).getTime() : null
    }
  }
}

// 处理菜单选择
const handleMenuSelect = (key: string) => {
  activeKey.value = key
  const stage = allStages.value.find(s => s.key === key)
  if (stage) {
    activeStage.value = { ...stage }
  }
}

// 在组件挂载时默认选择第一个阶段
onMounted(() => {
  if (mainProcessOptions.value.length > 0) {
    handleMenuSelect(mainProcessOptions.value[0].key)
  }
})
</script>

<style scoped>
/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 自定义滚动条 */
:deep(.n-scrollbar-content) {
  padding: 8px 0;
}

:deep(.n-scrollbar-rail) {
  right: 2px !important;
}

/* 卡片悬停效果 */
.process-item {
  transition: all 0.2s ease;
}

/* 多行文本省略 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 暗黑模式适配 */
.dark .n-card {
  background-color: rgba(17, 24, 39, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 标签页样式 */
:deep(.n-tabs-tab) {
  padding: 0 16px 12px;
  font-size: 14px;
  transition: color 0.2s;
}

:deep(.n-tabs-tab--active) {
  font-weight: 500;
  color: var(--n-tab-text-color-active);
}

:deep(.n-tabs-bar) {
  height: 2px;
  border-radius: 1px;
}
/* 菜单项样式 */
:deep(.process-menu) {
  width: 100%;
  --n-item-height: auto;
  --n-item-padding: 8px 0;
}

:deep(.process-menu .n-menu-item) {
  margin-bottom: 12px;
  border-radius: 8px;
  transition: all 0.3s;
  width: 100%;
  min-height: auto;
  background: transparent !important;
  padding: 0;
}

:deep(.process-menu .n-menu-item-content) {
  align-items: flex-start;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  min-height: auto;
  padding: 0;
  background: transparent !important;
}

:deep(.process-menu .n-menu-item-content__content) {
  width: 100%;
  min-width: 0;
  padding: 0;
}

:deep(.process-menu .n-menu-item-content__content-inner) {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 0;
  padding: 0;
}

/* 卡片悬停效果 */
:deep(.process-menu .n-menu-item-content--hover) {
  background: transparent !important;
}

:deep(.process-menu .n-menu-item-content--selected) {
  background: transparent !important;
  box-shadow: none !important;
}

/* 多行文本省略 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 确保文本不会溢出 */
:deep(.n-menu-item-content) {
  overflow: visible;
}

:deep(.n-menu-item-content__content) {
  overflow: visible;
  text-overflow: ellipsis;
  white-space: normal;
}

/* 进度条样式 */
.progress-bar {
  height: 4px;
  border-radius: 2px;
  background-color: #f0f0f0;
  overflow: hidden;
  margin-top: 4px;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 2px;
  background-color: var(--n-item-icon-color-active);
  transition: width 0.3s;
}
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.dark ::-webkit-scrollbar-track {
  background-color: #1f2937;
}
</style>