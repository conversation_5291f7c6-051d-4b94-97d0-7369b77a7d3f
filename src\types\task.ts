// 任务问题类型
export type TaskIssueType = 'task' | 'qa issue' | 'qa cr' | 'uat issue' | 'uat cr' | 'ma cr' | 'ma issue'

// 任务状态
export type TaskStatus = 'todo' | 'doing' | 'toverify' | 'done'

// 任务优先级
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent'

// 任务接口定义
export interface Task {
  id: number // 序号
  title: string // 标题
  issueType: TaskIssueType // 问题类型
  assignee: string // 指派人
  assigneeAvatar?: string // 指派人头像
  reporter: string // 报告人
  reporterAvatar?: string // 报告人头像
  description?: string // 描述
  status: TaskStatus // 状态
  plannedStartTime?: string // 计划开始时间
  plannedEndTime?: string // 计划结束时间
  actualStartTime?: string // 实际开始时间
  actualEndTime?: string // 实际结束时间
  duration?: number // 时长(小时)
  priority: TaskPriority // 优先级
  projectId?: number
  projectName?: string
}

// 任务问题类型配置
export const TASK_ISSUE_TYPE_CONFIG: Record<TaskIssueType, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
  'task': { text: '任务', type: 'default' },
  'qa issue': { text: 'QA问题', type: 'error' },
  'qa cr': { text: 'QA变更', type: 'warning' },
  'uat issue': { text: 'UAT问题', type: 'error' },
  'uat cr': { text: 'UAT变更', type: 'warning' },
  'ma cr': { text: 'MA变更', type: 'info' },
  'ma issue': { text: 'MA问题', type: 'error' }
}

// 任务状态配置
export const TASK_STATUS_CONFIG: Record<TaskStatus, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
  'todo': { text: '待办', type: 'default' },
  'doing': { text: '进行中', type: 'primary' },
  'toverify': { text: '待验证', type: 'warning' },
  'done': { text: '已完成', type: 'success' }
}

// 任务优先级配置
export const TASK_PRIORITY_CONFIG: Record<TaskPriority, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
  'low': { text: '低', type: 'info' },
  'medium': { text: '中', type: 'success' },
  'high': { text: '高', type: 'warning' },
  'urgent': { text: '紧急', type: 'error' }
}

// 任务问题类型选项
export const TASK_ISSUE_TYPE_OPTIONS = [
  { label: '任务', value: 'task' as TaskIssueType },
  { label: 'QA问题', value: 'qa issue' as TaskIssueType },
  { label: 'QA变更', value: 'qa cr' as TaskIssueType },
  { label: 'UAT问题', value: 'uat issue' as TaskIssueType },
  { label: 'UAT变更', value: 'uat cr' as TaskIssueType },
  { label: 'MA变更', value: 'ma cr' as TaskIssueType },
  { label: 'MA问题', value: 'ma issue' as TaskIssueType }
]

// 任务状态选项
export const TASK_STATUS_OPTIONS = [
  { label: '待办', value: 'todo' as TaskStatus },
  { label: '进行中', value: 'doing' as TaskStatus },
  { label: '待验证', value: 'toverify' as TaskStatus },
  { label: '已完成', value: 'done' as TaskStatus }
]

// 任务优先级选项
export const TASK_PRIORITY_OPTIONS = [
  { label: '低', value: 'low' as TaskPriority },
  { label: '中', value: 'medium' as TaskPriority },
  { label: '高', value: 'high' as TaskPriority },
  { label: '紧急', value: 'urgent' as TaskPriority }
]

// 获取问题类型显示信息
export const getIssueTypeInfo = (issueType: TaskIssueType) => {
  return TASK_ISSUE_TYPE_CONFIG[issueType] || { text: issueType, type: 'default' as const }
}

// 获取状态显示信息
export const getStatusInfo = (status: TaskStatus) => {
  return TASK_STATUS_CONFIG[status] || { text: status, type: 'default' as const }
}

// 获取优先级显示信息
export const getPriorityInfo = (priority: TaskPriority) => {
  return TASK_PRIORITY_CONFIG[priority] || { text: priority, type: 'default' as const }
}

// 格式化日期
export const formatDate = (dateStr?: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 看板列配置
export const KANBAN_COLUMNS = [
  { key: 'todo', title: '待办', status: 'todo' as TaskStatus, color: '#d9d9d9' },
  { key: 'doing', title: '进行中', status: 'doing' as TaskStatus, color: '#1890ff' },
  { key: 'toverify', title: '待验证', status: 'toverify' as TaskStatus, color: '#fa8c16' },
  { key: 'done', title: '已完成', status: 'done' as TaskStatus, color: '#52c41a' }
]
