<template>
  <div class="agent-chat h-full flex flex-col">
    <!-- 聊天头部 -->
    <n-card :bordered="false" class="border-b border-gray-100 dark:border-gray-700 rounded-none" size="small">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <n-avatar
            round
            :size="36"
            :src="agentInfo.avatar"
            class="mr-3"
          >
            <template #default>{{ agentInfo.name?.charAt(0) }}</template>
          </n-avatar>
          <div>
            <div class="font-medium text-base">{{ agentInfo.name }}</div>
            <div class="text-xs text-gray-500">
              {{ agentInfo.status === 'online' ? '在线' : '离线' }}
              <n-badge :type="agentInfo.status === 'online' ? 'success' : 'default'" dot class="ml-1" />
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button text @click="toggleFullscreen">
                <n-icon :size="18">
                  <FullscreenOutlined v-if="!isFullscreen" />
                  <FullscreenExitOutlined v-else />
                </n-icon>
              </n-button>
            </template>
            {{ isFullscreen ? '退出全屏' : '全屏' }}
          </n-tooltip>
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button text @click="clearMessages">
                <n-icon :size="18">
                  <DeleteOutlined />
                </n-icon>
              </n-button>
            </template>
            清空对话
          </n-tooltip>
        </div>
      </div>
    </n-card>

    <!-- 聊天消息区域 -->
    <div ref="messageContainer" class="flex-1 overflow-y-auto p-4 space-y-4">
      <template v-if="messages.length > 0">
        <div
          v-for="(message, index) in messages"
          :key="index"
          :class="[
            'flex',
            message.role === 'user' ? 'justify-end' : 'justify-start'
          ]"
        >
          <div
            :class="[
              'max-w-3/4 rounded-lg p-3',
              message.role === 'user'
                ? 'bg-blue-500 text-white rounded-br-none'
                : 'bg-gray-100 dark:bg-gray-700 rounded-bl-none'
            ]"
          >
            <div class="whitespace-pre-wrap">{{ message.content }}</div>
            <div class="text-xs mt-1 opacity-70 text-right">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
      </template>
      <div v-else class="h-full flex items-center justify-center text-gray-400">
        <n-empty description="开始与AI助手对话" />
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-start">
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 rounded-bl-none">
          <n-space align="center">
            <n-spin size="small" />
            <span>思考中...</span>
          </n-space>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="border-t border-gray-100 dark:border-gray-700 p-4">
      <n-input
        v-model:value="inputMessage"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 4 }"
        placeholder="输入消息..."
        :disabled="loading"
        @keydown.enter.exact.prevent="sendMessage"
      >
        <template #suffix>
          <n-button
            type="primary"
            text
            :disabled="!inputMessage.trim() || loading"
            @click="sendMessage"
          >
            <template #icon>
              <n-icon><SendOutlined /></n-icon>
            </template>
          </n-button>
        </template>
      </n-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import {
  SendOutlined,
  DeleteOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@vicons/antd'

const route = useRoute()
const message = useMessage()
const dialog = useDialog()

// Agent信息
const agentId = computed(() => route.params.id as string)
const agentInfo = ref({
  id: agentId.value,
  name: 'AI 助手',
  avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
  description: '智能AI助手，随时为您服务',
  status: 'online' as 'online' | 'offline'
})

// 消息相关
const inputMessage = ref('')
const messages = ref<Array<{
  role: 'user' | 'assistant'
  content: string
  timestamp: number
}>>([])

// 加载状态
const loading = ref(false)
const messageContainer = ref<HTMLElement | null>(null)

// 全屏状态
const isFullscreen = ref(false)

// 发送消息
const sendMessage = async () => {
  const content = inputMessage.value.trim()
  if (!content || loading.value) return

  // 添加用户消息
  const userMessage = {
    role: 'user' as const,
    content,
    timestamp: Date.now()
  }
  messages.value.push(userMessage)
  inputMessage.value = ''
  
  // 滚动到底部
  scrollToBottom()

  // 设置加载状态
  loading.value = true

  try {
    // 模拟AI回复
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 添加AI回复
    const aiMessage = {
      role: 'assistant' as const,
      content: `这是对"${content}"的回复。这是一个模拟回复，实际使用中这里会调用AI接口获取真实回复。`,
      timestamp: Date.now()
    }
    messages.value.push(aiMessage)
    
    // 滚动到底部
    scrollToBottom()
  } catch (error) {
    console.error('发送消息失败:', error)
    message.error('发送失败，请重试')
  } finally {
    loading.value = false
  }
}

// 清空消息
const clearMessages = () => {
  dialog.warning({
    title: '确认清空',
    content: '确定要清空当前对话记录吗？此操作不可恢复。',
    positiveText: '清空',
    negativeText: '取消',
    onPositiveClick: () => {
      messages.value = []
      message.success('已清空对话记录')
    }
  })
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }
}

// 监听全屏变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

// 加载对话历史
const loadChatHistory = () => {
  // 这里可以加载本地存储的对话历史
  const savedMessages = localStorage.getItem(`chat_${agentId.value}`)
  if (savedMessages) {
    try {
      messages.value = JSON.parse(savedMessages)
    } catch (error) {
      console.error('加载对话历史失败:', error)
    }
  }
}

// 保存对话历史
const saveChatHistory = () => {
  if (messages.value.length > 0) {
    localStorage.setItem(`chat_${agentId.value}`, JSON.stringify(messages.value))
  }
}

// 组件挂载时
onMounted(() => {
  loadChatHistory()
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  
  // 添加一个欢迎消息
  if (messages.value.length === 0) {
    messages.value.push({
      role: 'assistant',
      content: '你好！我是AI助手，有什么可以帮您的吗？',
      timestamp: Date.now()
    })
  }
  
  // 滚动到底部
  scrollToBottom()
})

// 组件卸载时
onUnmounted(() => {
  saveChatHistory()
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    // 保存当前对话
    saveChatHistory()
    // 加载新对话
    loadChatHistory()
  }
})
</script>

<style scoped>
.agent-chat {
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
}

.message-container {
  scroll-behavior: smooth;
}

/* 自定义滚动条 */
:deep(::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 暗黑模式滚动条 */n-dark :deep(::-webkit-scrollbar-thumb) {
  background: #4b5563;
}

n-dark :deep(::-webkit-scrollbar-thumb:hover) {
  background: #6b7280;
}

/* 输入框样式 */
:deep(.n-input) {
  border-radius: 8px;
}

:deep(.n-input__border) {
  border-color: #e5e7eb;
}

:deep(.n-input--focus .n-input__border) {
  border-color: var(--n-color-focus);
  box-shadow: 0 0 0 2px var(--n-box-shadow-focus);
}

/* 暗黑模式输入框 */n-dark :deep(.n-input__border) {
  border-color: #374151;
  background-color: #1f2937;
}

/* 按钮悬停效果 */
:deep(.n-button) {
  transition: all 0.2s;
}

:deep(.n-button--primary-type) {
  background-color: var(--n-color);
}

:deep(.n-button--primary-type:hover) {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 消息气泡动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.message-enter-active) {
  animation: fadeIn 0.3s ease-out;
}

:deep(.message-leave-active) {
  animation: fadeIn 0.3s reverse;
}
</style>
