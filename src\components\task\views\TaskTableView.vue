<script setup lang="ts">
import { computed, h } from 'vue'
import type { Task } from '../TaskView.vue'

interface Props {
  tasks: Task[]
  loading?: boolean
  editable?: boolean
}

interface Emits {
  (e: 'edit', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'view', task: Task): void
  (e: 'fieldUpdate', task: Task, field: keyof Task, value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  editable: false
})

const emit = defineEmits<Emits>()

// 获取问题类型显示信息
const getIssueTypeInfo = (issueType: string) => {
  const issueTypeMap: Record<string, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
    bug: { text: '缺陷', type: 'error' },
    feature: { text: '新功能', type: 'primary' },
    task: { text: '任务', type: 'default' },
    improvement: { text: '改进', type: 'info' },
    story: { text: '用户故事', type: 'success' }
  }
  return issueTypeMap[issueType] || { text: issueType, type: 'default' }
}

// 获取状态显示信息
const getStatusInfo = (status: string) => {
  const statusMap: Record<string, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
    todo: { text: '未开始', type: 'default' },
    in_progress: { text: '进行中', type: 'primary' },
    done: { text: '已完成', type: 'success' },
    cancelled: { text: '已取消', type: 'error' },
    blocked: { text: '阻塞', type: 'warning' }
  }
  return statusMap[status] || { text: status, type: 'default' }
}

// 获取优先级显示信息
const getPriorityInfo = (priority: string) => {
  const priorityMap: Record<string, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
    low: { text: '低', type: 'info' },
    medium: { text: '中', type: 'success' },
    high: { text: '高', type: 'warning' },
    urgent: { text: '紧急', type: 'error' }
  }
  return priorityMap[priority] || { text: priority, type: 'default' }
}

// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 问题类型选项
const issueTypeOptions = [
  { label: '缺陷', value: 'bug' },
  { label: '新功能', value: 'feature' },
  { label: '任务', value: 'task' },
  { label: '改进', value: 'improvement' },
  { label: '用户故事', value: 'story' }
]

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'todo' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'done' },
  { label: '已取消', value: 'cancelled' },
  { label: '阻塞', value: 'blocked' }
]

// 优先级选项
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 字段更新处理
const handleFieldUpdate = (task: Task, field: keyof Task, value: any) => {
  emit('fieldUpdate', task, field, value)
}

// 表格列配置
const tableColumns = computed(() => [
  {
    title: '序号',
    key: 'id',
    width: 80,
    render: (row: Task) => row.id
  },
  {
    title: '标题',
    key: 'title',
    width: 200,
    ellipsis: true,
    render: (row: Task) => props.editable ?
      h('n-input', {
        value: row.title,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'title', value)
      }) : row.title
  },
  {
    title: '问题类型',
    key: 'issueType',
    width: 100,
    render: (row: Task) => props.editable ?
      h('n-select', {
        value: row.issueType,
        options: issueTypeOptions,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'issueType', value)
      }) :
      h('n-tag', {
        type: getIssueTypeInfo(row.issueType).type,
        size: 'small'
      }, { default: () => getIssueTypeInfo(row.issueType).text })
  },
  {
    title: '指派人',
    key: 'assignee',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-input', {
        value: row.assignee,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'assignee', value)
      }) : row.assignee
  },
  {
    title: '报告人',
    key: 'reporter',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-input', {
        value: row.reporter,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'reporter', value)
      }) : row.reporter
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: true,
    render: (row: Task) => props.editable ?
      h('n-input', {
        value: row.description || '',
        type: 'textarea',
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'description', value)
      }) : (row.description || '-')
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-select', {
        value: row.status,
        options: statusOptions,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'status', value)
      }) :
      h('n-tag', {
        type: getStatusInfo(row.status).type,
        size: 'small'
      }, { default: () => getStatusInfo(row.status).text })
  },
  {
    title: '计划开始时间',
    key: 'plannedStartTime',
    width: 140,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: row.plannedStartTime ? new Date(row.plannedStartTime).getTime() : null,
        type: 'date',
        onUpdateValue: (value: number | null) => handleFieldUpdate(row, 'plannedStartTime', value ? new Date(value).toISOString().split('T')[0] : null)
      }) : formatDate(row.plannedStartTime)
  },
  {
    title: '计划结束时间',
    key: 'plannedEndTime',
    width: 140,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: row.plannedEndTime ? new Date(row.plannedEndTime).getTime() : null,
        type: 'date',
        onUpdateValue: (value: number | null) => handleFieldUpdate(row, 'plannedEndTime', value ? new Date(value).toISOString().split('T')[0] : null)
      }) : formatDate(row.plannedEndTime)
  },
  {
    title: '实际开始时间',
    key: 'actualStartTime',
    width: 140,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: row.actualStartTime ? new Date(row.actualStartTime).getTime() : null,
        type: 'date',
        onUpdateValue: (value: number | null) => handleFieldUpdate(row, 'actualStartTime', value ? new Date(value).toISOString().split('T')[0] : null)
      }) : formatDate(row.actualStartTime)
  },
  {
    title: '实际结束时间',
    key: 'actualEndTime',
    width: 140,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: row.actualEndTime ? new Date(row.actualEndTime).getTime() : null,
        type: 'date',
        onUpdateValue: (value: number | null) => handleFieldUpdate(row, 'actualEndTime', value ? new Date(value).toISOString().split('T')[0] : null)
      }) : formatDate(row.actualEndTime)
  },
  {
    title: '时长(小时)',
    key: 'duration',
    width: 100,
    render: (row: Task) => props.editable ?
      h('n-input-number', {
        value: row.duration || 0,
        min: 0,
        onUpdateValue: (value: number) => handleFieldUpdate(row, 'duration', value)
      }) : (row.duration || '-')
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100,
    render: (row: Task) => props.editable ?
      h('n-select', {
        value: row.priority,
        options: priorityOptions,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'priority', value)
      }) :
      h('n-tag', {
        type: getPriorityInfo(row.priority).type,
        size: 'small'
      }, { default: () => getPriorityInfo(row.priority).text })
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right' as const,
    render: (row: Task) => h('div', { class: 'flex gap-2' }, [
      h('n-button', {
        size: 'small',
        text: true,
        type: 'primary',
        onClick: () => emit('edit', row)
      }, { default: () => '编辑' }),
      h('n-button', {
        size: 'small',
        text: true,
        type: 'error',
        onClick: () => emit('delete', row)
      }, { default: () => '删除' })
    ])
  }
])

// 事件处理
const handleEdit = (task: Task) => emit('edit', task)
const handleDelete = (task: Task) => emit('delete', task)
const handleView = (task: Task) => emit('view', task)
</script>

<template>
  <div class="table-view">
    <div v-if="tasks.length === 0" class="empty-state">
      <n-empty description="暂无任务" />
    </div>
    
    <n-data-table
      v-else
      :columns="tableColumns"
      :data="tasks"
      :bordered="false"
      :single-line="false"
      :row-key="(row: Task) => row.id"
      :loading="loading"
      class="task-table"
    />
  </div>
</template>

<style lang="less" scoped>
.table-view {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-table {
  min-height: 400px;
}

:deep(.n-data-table-th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 12px 16px;
}

:deep(.n-data-table-tr:hover) {
  background: #f5f5f5;
}
</style>
