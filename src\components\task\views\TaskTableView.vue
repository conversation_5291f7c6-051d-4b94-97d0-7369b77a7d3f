<script setup lang="ts">
import { computed, h } from 'vue'
import type { Task } from '../TaskView.vue'

interface Props {
  tasks: Task[]
  loading?: boolean
  editable?: boolean
}

interface Emits {
  (e: 'edit', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'view', task: Task): void
  (e: 'fieldUpdate', task: Task, field: keyof Task, value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  editable: false
})

const emit = defineEmits<Emits>()

// 获取状态显示信息
const getStatusInfo = (status: string) => {
  const statusMap: Record<string, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
    todo: { text: '未开始', type: 'default' },
    in_progress: { text: '进行中', type: 'primary' },
    done: { text: '已完成', type: 'success' },
    cancelled: { text: '已取消', type: 'error' }
  }
  return statusMap[status] || { text: status, type: 'default' }
}

// 获取优先级显示信息
const getPriorityInfo = (priority: string) => {
  const priorityMap: Record<string, { text: string; type: 'default' | 'primary' | 'success' | 'error' | 'info' | 'warning' }> = {
    low: { text: '低', type: 'info' },
    medium: { text: '中', type: 'success' },
    high: { text: '高', type: 'warning' },
    urgent: { text: '紧急', type: 'error' }
  }
  return priorityMap[priority] || { text: priority, type: 'default' }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'todo' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'done' },
  { label: '已取消', value: 'cancelled' }
]

// 优先级选项
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 字段更新处理
const handleFieldUpdate = (task: Task, field: keyof Task, value: any) => {
  emit('fieldUpdate', task, field, value)
}

// 表格列配置
const tableColumns = computed(() => [
  {
    title: '任务名称',
    key: 'name',
    width: 200,
    ellipsis: true,
    render: (row: Task) => props.editable ? 
      h('n-input', { 
        value: row.name, 
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'name', value)
      }) : row.name
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-select', {
        value: row.status,
        options: statusOptions,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'status', value)
      }) :
      h('n-tag', {
        type: getStatusInfo(row.status).type,
        size: 'small'
      }, { default: () => getStatusInfo(row.status).text })
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100,
    render: (row: Task) => props.editable ?
      h('n-select', {
        value: row.priority,
        options: priorityOptions,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'priority', value)
      }) :
      h('n-tag', {
        type: getPriorityInfo(row.priority).type,
        size: 'small'
      }, { default: () => getPriorityInfo(row.priority).text })
  },
  {
    title: '负责人',
    key: 'assigneeName',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-input', {
        value: row.assigneeName,
        onUpdateValue: (value: string) => handleFieldUpdate(row, 'assigneeName', value)
      }) : row.assigneeName
  },
  {
    title: '进度',
    key: 'progress',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-input-number', {
        value: row.progress,
        min: 0,
        max: 100,
        onUpdateValue: (value: number) => handleFieldUpdate(row, 'progress', value)
      }) :
      h('div', { class: 'flex items-center gap-2' }, [
        h('n-progress', {
          percentage: row.progress,
          showIndicator: false,
          height: 6,
          style: { flex: 1 }
        }),
        h('span', { class: 'text-xs text-gray-500' }, `${row.progress}%`)
      ])
  },
  {
    title: '截止日期',
    key: 'dueDate',
    width: 120,
    render: (row: Task) => props.editable ?
      h('n-date-picker', {
        value: new Date(row.dueDate).getTime(),
        type: 'date',
        onUpdateValue: (value: number) => handleFieldUpdate(row, 'dueDate', new Date(value).toISOString().split('T')[0])
      }) : formatDate(row.dueDate)
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row: Task) => h('div', { class: 'flex gap-2' }, [
      h('n-button', {
        size: 'small',
        text: true,
        type: 'primary',
        onClick: () => emit('edit', row)
      }, { default: () => '编辑' }),
      h('n-button', {
        size: 'small',
        text: true,
        type: 'error',
        onClick: () => emit('delete', row)
      }, { default: () => '删除' })
    ])
  }
])

// 事件处理
const handleEdit = (task: Task) => emit('edit', task)
const handleDelete = (task: Task) => emit('delete', task)
const handleView = (task: Task) => emit('view', task)
</script>

<template>
  <div class="table-view">
    <div v-if="tasks.length === 0" class="empty-state">
      <n-empty description="暂无任务" />
    </div>
    
    <n-data-table
      v-else
      :columns="tableColumns"
      :data="tasks"
      :bordered="false"
      :single-line="false"
      :row-key="(row: Task) => row.id"
      :loading="loading"
      class="task-table"
    />
  </div>
</template>

<style lang="less" scoped>
.table-view {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-table {
  min-height: 400px;
}

:deep(.n-data-table-th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 12px 16px;
}

:deep(.n-data-table-tr:hover) {
  background: #f5f5f5;
}
</style>
