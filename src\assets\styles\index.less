// 引入变量
@import './variables';

// 引入全局样式
@import './global';

// 引入组件样式
@import './card';

// 工具类
.text-ellipsis {
  .text-ellipsis();
}

.pointer {
  cursor: pointer;
}

// 布局
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-col-center {
  .flex-center();
  flex-direction: column;
}

// 边距工具类
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }

.mt-1 { margin-top: 4px; }
.mr-1 { margin-right: 4px; }
.mb-1 { margin-bottom: 4px; }
.ml-1 { margin-left: 4px; }

.mt-2 { margin-top: 8px; }
.mr-2 { margin-right: 8px; }
.mb-2 { margin-bottom: 8px; }
.ml-2 { margin-left: 8px; }

// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

// 文本颜色
.text-primary { color: @primary-color; }
.text-success { color: @success-color; }
.text-warning { color: @warning-color; }
.text-error { color: @error-color; }
.text-disabled { color: @text-color-disabled; }

// 背景色
.bg-primary { background-color: @primary-color; }
.bg-success { background-color: @success-color; }
.bg-warning { background-color: @warning-color; }
.bg-error { background-color: @error-color; }
.bg-disabled { background-color: @bg-color; }
