<template>
  <page-layout title="任务列表">
    <n-card>
      <n-data-table
        :columns="columns"
        :data="tasks"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        :row-key="row => row.id"
        @update:sorter="handleSorterChange"
      >
        <!-- 优先级列 -->
        <template #priority="{ row }">
          <n-tag :type="getPriorityType(row.priority)" size="small">
            {{ getPriorityText(row.priority) }}
          </n-tag>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <n-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </n-tag>
        </template>

        <!-- 进度列 -->
        <template #progress="{ row }">
          {{ row.progress }}%
        </template>

        <!-- 操作列 -->
        <template #actions="{ row }">
          <div class="flex gap-2">
            <n-button size="small" @click="handleView(row)">
              查看
            </n-button>
            <n-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </n-button>
          </div>
        </template>
      </n-data-table>
    </n-card>
  </page-layout>
</template>

<script setup lang="ts">
import PageLayout from "@/layouts/PageLayout.vue";

// 定义任务类型
interface Task {
  id: number
  name: string
  projectName: string
  assigneeName: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'todo' | 'in_progress' | 'done' | 'cancelled'
  dueDate: string
  progress: number
}

// 使用 UnoCSS 图标
const PlusOutlined = 'i-ant-design:plus-outlined'

// 模拟数据
const tasks = ref<Task[]>([
  {
    id: 1,
    name: '开发登录页面',
    projectName: '项目A',
    assigneeName: '张三',
    priority: 'high',
    status: 'in_progress',
    dueDate: '2023-12-31',
    progress: 60
  },
  {
    id: 2,
    name: '设计数据库模型',
    projectName: '项目B',
    assigneeName: '李四',
    priority: 'medium',
    status: 'todo',
    dueDate: '2023-12-15',
    progress: 0
  },
  {
    id: 3,
    name: '编写单元测试',
    projectName: '项目A',
    assigneeName: '王五',
    priority: 'low',
    status: 'done',
    dueDate: '2023-12-10',
    progress: 100
  }
])

const loading = ref(false)

interface Pagination {
  page: number
  pageSize: number
  showSizePicker: boolean
  pageSizes: number[]
  itemCount: number
  onChange: (page: number) => void
  onUpdatePageSize: (pageSize: number) => void
}

const pagination = ref<Pagination>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0,
  onChange: (page: number) => {
    pagination.value.page = page
    fetchData()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
    fetchData()
  }
})

const columns = [
  {
    title: '任务名称',
    key: 'name',
    sorter: 'default'
  },
  {
    title: '项目',
    key: 'projectName',
    sorter: 'default'
  },
  {
    title: '负责人',
    key: 'assigneeName',
    sorter: 'default'
  },
  {
    title: '优先级',
    key: 'priority',
    sorter: true
  },
  {
    title: '状态',
    key: 'status',
    sorter: true
  },
  {
    title: '截止日期',
    key: 'dueDate',
    sorter: true
  },
  {
    title: '进度',
    key: 'progress',
    sorter: true
  },
  {
    title: '操作',
    key: 'actions'
  }
]

// 获取状态文本
const getStatusText = (status: Task['status']) => {
  const statusMap: Record<string, string> = {
    todo: '未开始',
    in_progress: '进行中',
    done: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取状态类型
const getStatusType = (status: Task['status']) => {
  const typeMap: Record<string, string> = {
    todo: 'default',
    in_progress: 'primary',
    done: 'success',
    cancelled: 'error'
  }
  return typeMap[status] || 'default'
}

// 获取优先级文本
const getPriorityText = (priority: Task['priority']) => {
  const priorityMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

// 获取优先级类型
const getPriorityType = (priority: Task['priority']) => {
  const typeMap: Record<string, string> = {
    low: 'info',
    medium: 'success',
    high: 'warning',
    urgent: 'error'
  }
  return typeMap[priority] || 'default'
}

// 查看任务
const handleView = (task: Task) => () => {
  // 跳转到任务详情页
  console.log('View task:', task)
}

// 编辑任务
const handleEdit = (task: Task) => () => {
  // 打开编辑弹窗
  console.log('Edit task:', task)
}

// 创建任务
const handleCreate = () => {
  console.log('Create new task')
}

// 排序变化
const handleSorterChange = (sorter: any) => {
  console.log('Sorter changed:', sorter)
  fetchData()
}

// 获取任务数据
const fetchData = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取任务列表
    // 这里使用模拟数据
    pagination.value.itemCount = tasks.value.length
  } catch (error) {
    console.error('获取任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.task-list {
  padding: 16px;
}
</style>
