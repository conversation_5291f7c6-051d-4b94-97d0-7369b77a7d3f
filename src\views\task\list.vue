<template>
  <page-layout title="任务管理">
    <TaskView
      :tasks="tasks"
      :projects="projects"
      :loading="loading"
      :show-project-filter="true"
      title="全部任务"
      view-mode="list"
      :editable="false"
      @create="handleCreate"
      @edit="handleEdit"
      @delete="handleDelete"
      @view="handleView"
    />
  </page-layout>
</template>

<script setup lang="ts">
import PageLayout from "@/layouts/PageLayout.vue";
import TaskView, { type Task, type Project } from "@/components/task/TaskView.vue";
import { useMessage, useDialog } from 'naive-ui';

const message = useMessage();
const dialog = useDialog();

// 模拟项目数据
const projects = ref<Project[]>([
  { id: 1, name: 'PPMS项目管理系统', color: '#1890ff' },
  { id: 2, name: '企业官网改版', color: '#52c41a' },
  { id: 3, name: '移动端APP开发', color: '#fa8c16' },
  { id: 4, name: '数据中台建设', color: '#722ed1' },
  { id: 5, name: 'AI智能客服系统', color: '#eb2f96' }
]);

// 模拟任务数据
const tasks = ref<Task[]>([
  {
    id: 1,
    title: '开发用户登录功能',
    issueType: 'task',
    assignee: '张三',
    assigneeAvatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
    reporter: '产品经理',
    description: '实现用户登录、注册、密码重置等功能',
    status: 'doing',
    plannedStartTime: '2024-01-01',
    plannedEndTime: '2024-01-15',
    actualStartTime: '2024-01-02',
    duration: 80,
    priority: 'high',
    projectId: 1,
    projectName: 'PPMS项目管理系统'
  },
  {
    id: 2,
    title: '修复登录页面样式问题',
    issueType: 'qa issue',
    assignee: '李四',
    reporter: '测试工程师',
    description: '登录页面在移动端显示异常，需要修复响应式布局',
    status: 'todo',
    plannedStartTime: '2024-01-05',
    plannedEndTime: '2024-01-10',
    duration: 16,
    priority: 'urgent',
    projectId: 1,
    projectName: 'PPMS项目管理系统'
  },
  {
    id: 3,
    title: '编写API接口文档',
    issueType: 'task',
    assignee: '王五',
    reporter: '技术负责人',
    description: '编写RESTful API接口文档，包括请求参数和响应格式',
    status: 'done',
    plannedStartTime: '2024-01-01',
    plannedEndTime: '2024-01-05',
    actualStartTime: '2024-01-01',
    actualEndTime: '2024-01-04',
    duration: 32,
    priority: 'medium',
    projectId: 2,
    projectName: '企业官网改版'
  },
  {
    id: 4,
    title: '优化数据库查询性能',
    issueType: 'qa cr',
    assignee: '赵六',
    reporter: '运维工程师',
    description: '优化用户查询和项目统计相关的数据库查询性能',
    status: 'toverify',
    plannedStartTime: '2024-01-10',
    plannedEndTime: '2024-01-20',
    actualStartTime: '2024-01-12',
    duration: 40,
    priority: 'high',
    projectId: 3,
    projectName: '移动端APP开发'
  },
  {
    id: 5,
    title: 'UAT环境部署问题',
    issueType: 'uat issue',
    assignee: '孙七',
    reporter: '产品经理',
    description: 'UAT环境部署失败，需要排查并修复部署脚本',
    status: 'doing',
    plannedStartTime: '2024-01-08',
    plannedEndTime: '2024-01-12',
    duration: 24,
    priority: 'urgent',
    projectId: 4,
    projectName: '数据中台建设'
  },
  {
    id: 6,
    title: 'MA环境配置变更',
    issueType: 'ma cr',
    assignee: '钱八',
    reporter: '运维工程师',
    description: '生产环境配置需要更新，包括数据库连接和缓存配置',
    status: 'toverify',
    plannedStartTime: '2024-01-15',
    plannedEndTime: '2024-01-18',
    actualStartTime: '2024-01-15',
    duration: 16,
    priority: 'high',
    projectId: 1,
    projectName: 'PPMS项目管理系统'
  },
  {
    id: 7,
    title: '生产环境监控告警',
    issueType: 'ma issue',
    assignee: '周九',
    reporter: '运维工程师',
    description: '生产环境出现性能告警，需要紧急处理',
    status: 'doing',
    plannedStartTime: '2024-01-20',
    plannedEndTime: '2024-01-22',
    actualStartTime: '2024-01-20',
    duration: 12,
    priority: 'urgent',
    projectId: 2,
    projectName: '企业官网改版'
  }
]);

const loading = ref(false);

// 事件处理函数
const handleCreate = () => {
  message.info('创建新任务');
  // TODO: 打开创建任务弹窗或跳转到创建页面
};

const handleEdit = (task: Task) => {
  message.info(`编辑任务: ${task.title}`);
  // TODO: 打开编辑任务弹窗或跳转到编辑页面
};

const handleDelete = (task: Task) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除任务"${task.title}"吗？此操作不可恢复。`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      // 从任务列表中移除
      const index = tasks.value.findIndex(t => t.id === task.id);
      if (index > -1) {
        tasks.value.splice(index, 1);
        message.success('任务删除成功');
      }
    }
  });
};

const handleView = (task: Task) => {
  message.info(`查看任务: ${task.title}`);
  // TODO: 跳转到任务详情页面
};

// 获取任务数据
const fetchData = async () => {
  try {
    loading.value = true;
    // TODO: 调用API获取任务列表
    // 这里使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 500));
  } catch (error) {
    console.error('获取任务列表失败:', error);
    message.error('获取任务列表失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
/* 任务列表页面样式 */
</style>
