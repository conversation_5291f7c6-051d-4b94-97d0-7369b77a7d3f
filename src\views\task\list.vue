<template>
  <page-layout title="任务管理">
    <TaskView
      :tasks="tasks"
      :projects="projects"
      :loading="loading"
      :show-project-filter="true"
      title="全部任务"
      @create="handleCreate"
      @edit="handleEdit"
      @delete="handleDelete"
      @view="handleView"
    />
  </page-layout>
</template>

<script setup lang="ts">
import PageLayout from "@/layouts/PageLayout.vue";
import TaskView, { type Task, type Project } from "@/components/task/TaskView.vue";
import { useMessage, useDialog } from 'naive-ui';

const message = useMessage();
const dialog = useDialog();

// 模拟项目数据
const projects = ref<Project[]>([
  { id: 1, name: 'PPMS项目管理系统', color: '#1890ff' },
  { id: 2, name: '企业官网改版', color: '#52c41a' },
  { id: 3, name: '移动端APP开发', color: '#fa8c16' },
  { id: 4, name: '数据中台建设', color: '#722ed1' },
  { id: 5, name: 'AI智能客服系统', color: '#eb2f96' }
]);

// 模拟任务数据
const tasks = ref<Task[]>([
  {
    id: 1,
    name: '开发用户登录功能',
    projectId: 1,
    projectName: 'PPMS项目管理系统',
    assigneeName: '张三',
    assigneeAvatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
    priority: 'high',
    status: 'in_progress',
    dueDate: '2024-01-15',
    progress: 75,
    description: '实现用户登录、注册、密码重置等功能',
    tags: ['前端', 'Vue3']
  },
  {
    id: 2,
    name: '设计数据库表结构',
    projectId: 1,
    projectName: 'PPMS项目管理系统',
    assigneeName: '李四',
    priority: 'urgent',
    status: 'todo',
    dueDate: '2024-01-10',
    progress: 0,
    description: '设计用户、项目、任务等核心表结构',
    tags: ['数据库', 'MySQL']
  },
  {
    id: 3,
    name: '编写API接口文档',
    projectId: 2,
    projectName: '企业官网改版',
    assigneeName: '王五',
    priority: 'medium',
    status: 'done',
    dueDate: '2024-01-05',
    progress: 100,
    description: '编写RESTful API接口文档',
    tags: ['文档', 'API']
  },
  {
    id: 4,
    name: '移动端界面设计',
    projectId: 3,
    projectName: '移动端APP开发',
    assigneeName: '赵六',
    priority: 'high',
    status: 'in_progress',
    dueDate: '2024-01-20',
    progress: 45,
    description: '设计移动端用户界面和交互流程',
    tags: ['设计', 'UI/UX']
  },
  {
    id: 5,
    name: '数据清洗脚本开发',
    projectId: 4,
    projectName: '数据中台建设',
    assigneeName: '孙七',
    priority: 'low',
    status: 'cancelled',
    dueDate: '2024-01-12',
    progress: 20,
    description: '开发数据清洗和预处理脚本',
    tags: ['数据', 'Python']
  }
]);

const loading = ref(false);

// 事件处理函数
const handleCreate = () => {
  message.info('创建新任务');
  // TODO: 打开创建任务弹窗或跳转到创建页面
};

const handleEdit = (task: Task) => {
  message.info(`编辑任务: ${task.name}`);
  // TODO: 打开编辑任务弹窗或跳转到编辑页面
};

const handleDelete = (task: Task) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除任务"${task.name}"吗？此操作不可恢复。`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      // 从任务列表中移除
      const index = tasks.value.findIndex(t => t.id === task.id);
      if (index > -1) {
        tasks.value.splice(index, 1);
        message.success('任务删除成功');
      }
    }
  });
};

const handleView = (task: Task) => {
  message.info(`查看任务: ${task.name}`);
  // TODO: 跳转到任务详情页面
};

// 获取任务数据
const fetchData = async () => {
  try {
    loading.value = true;
    // TODO: 调用API获取任务列表
    // 这里使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 500));
  } catch (error) {
    console.error('获取任务列表失败:', error);
    message.error('获取任务列表失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
/* 任务列表页面样式 */
</style>
