import { defineStore } from 'pinia'

export const useThemeStore = defineStore('theme', () => {
  // 主题配置
  const themeOverrides = {
    common: {
      primaryColor: '#5b8bf7',
      primaryColorHover: '#7da3f8',
      primaryColorPressed: '#3a73f5',
      primaryColorSuppl: '#5b8bf7',
      heightMedium: '40px',
      borderRadius: '8px',
      fontSize: '14px',
      fontWeight: '500',
      bodyColor: '#ffffff',
      baseColor: '#ffffff',
      textColorBase: '#333333',
      textColor1: '#1a1a1a',
      textColor2: '#333333',
      textColor3: '#666666',
      cardColor: '#ffffff',
      modalColor: '#ffffff',
      popoverColor: '#ffffff',
      dividerColor: '#f0f0f0'
    },
    Menu: {
      itemTextColor: '#333333',
      itemTextColorHover: '#5b8bf7',
      itemTextColorActive: '#5b8bf7',
      itemTextColorChildActive: '#5b8bf7',
      itemIconColor: '#666666',
      itemIconColorHover: '#5b8bf7',
      itemIconColorActive: '#5b8bf7',
      itemIconColorChildActive: '#5b8bf7',
      itemColorActive: 'rgba(91, 139, 247, 0.1)',
      itemColorActiveHover: 'rgba(91, 139, 247, 0.15)',
      itemColorHover: 'rgba(0, 0, 0, 0.04)',
      itemHeight: '44px',
      itemPadding: '0 16px',
      itemMargin: '4px 0',
      arrowColor: '#999999',
      arrowColorHover: '#5b8bf7',
      arrowColorActive: '#5b8bf7',
      arrowColorChildActive: '#5b8bf7',
      groupTextColor: '#999999',
      groupTextFontSize: '12px',
      groupTextPadding: '12px 16px',
      groupTextFontWeight: '600',
      dividerColor: '#f0f0f0'
    },
    Button: {
      textColor: '#5b8bf7',
      textColorHover: '#7da3f8',
      textColorPressed: '#3a73f5',
      textColorFocus: '#5b8bf7',
      textColorDisabled: 'rgba(91, 139, 247, 0.5)',
      color: 'rgba(91, 139, 247, 0.1)',
      colorHover: 'rgba(91, 139, 247, 0.2)',
      colorPressed: 'rgba(91, 139, 247, 0.3)',
      colorFocus: 'rgba(91, 139, 247, 0.1)',
      colorDisabled: 'rgba(91, 139, 247, 0.08)',
      border: '1px solid rgba(91, 139, 247, 0.3)',
      borderHover: '1px solid rgba(91, 139, 247, 0.5)',
      borderPressed: '1px solid rgba(91, 139, 247, 0.7)',
      borderFocus: '1px solid rgba(91, 139, 247, 0.5)',
      rippleColor: 'rgba(91, 139, 247, 0.1)',
      paddingMedium: '0 16px',
      heightMedium: '36px',
      fontSizeMedium: '14px',
      borderRadiusMedium: '6px'
    },
    Input: {
      heightMedium: '36px',
      fontSizeMedium: '14px',
      paddingMedium: '0 12px',
      borderRadius: '6px',
      placeholderColor: '#999999',
      border: '1px solid #d9d9d9',
      borderHover: '1px solid #5b8bf7',
      borderFocus: '1px solid #5b8bf7',
      borderDisabled: '1px solid #f0f0f0',
      colorDisabled: '#bfbfbf',
      backgroundColor: '#ffffff',
      boxShadowFocus: '0 0 0 2px rgba(91, 139, 247, 0.2)',
      boxShadowFocusError: '0 0 0 2px rgba(237, 64, 20, 0.2)',
      boxShadowFocusWarning: '0 0 0 2px rgba(255, 153, 0, 0.2)',
      boxShadowFocusSuccess: '0 0 0 2px rgba(25, 190, 107, 0.2)',
      boxShadowFocusInfo: '0 0 0 2px rgba(91, 139, 247, 0.2)'
    },
    Card: {
      borderRadius: '8px',
      titleFontSizeMedium: '16px',
      titleFontWeight: '600',
      titleTextColor: '#1a1a1a',
      textColor: '#333333',
      color: '#ffffff',
      paddingMedium: '16px',
      titlePadding: '16px 16px 12px',
      contentPadding: '12px 16px 16px',
      footerPadding: '12px 16px 16px',
      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
      boxShadowHover: '0 4px 8px 0 rgba(0, 0, 0, 0.05), 0 2px 8px 0 rgba(0, 0, 0, 0.03)'
    },
    Tabs: {
      tabFontSizeMedium: '14px',
      tabGapMediumCard: '4px',
      tabTextColorActiveBar: '#5b8bf7',
      tabTextColorHoverBar: '#7da3f8',
      tabTextColorActiveLine: '#5b8bf7',
      tabTextColorHoverLine: '#7da3f8',
      tabTextColorActiveCard: '#5b8bf7',
      tabTextColorHoverCard: '#7da3f8',
      tabBorderColor: '#f0f0f0',
      tabColor: '#f5f5f5',
      tabBorderRadius: '8px',
      tabGapSmallLine: '32px',
      tabPaddingSmallLine: '8px 0',
      tabPaddingMediumLine: '12px 0',
      tabPaddingLargeLine: '16px 0',
      tabPaddingSmallBar: '8px 0',
      tabPaddingMediumBar: '12px 0',
      tabPaddingLargeBar: '16px 0',
      tabPaddingSmallCard: '8px 16px',
      tabPaddingMediumCard: '12px 16px',
      tabPaddingLargeCard: '16px 16px',
      tabGapSmallBar: '32px',
      tabGapMediumBar: '32px',
      tabGapLargeBar: '32px',
      tabGapSmallSegment: '0',
      tabGapMediumSegment: '0',
      tabGapLargeSegment: '0',
      tabPaddingSmallSegment: '8px 16px',
      tabPaddingMediumSegment: '12px 16px',
      tabPaddingLargeSegment: '16px 16px',
      tabTextColorSegment: '#666666',
      tabTextColorHoverSegment: '#5b8bf7',
      tabTextColorActiveSegment: '#ffffff',
      tabColorSegment: '#f5f5f5',
      tabColorActiveSegment: '#5b8bf7',
      tabBorderRadiusSegment: '6px',
      tabFontWeightActive: '500',
      tabFontWeight: '400',
      tabTextColor: '#666666',
      tabTextColorDisabled: '#bfbfbf'
    },
    Layout: {
      headerColor: '#ffffff',
      headerBorderColor: '#f0f0f0',
      siderColor: '#ffffff',
      siderBorderColor: '#f0f0f0',
      footerColor: '#ffffff',
      footerBorderColor: '#f0f0f0'
    }
  }

  return {
    themeOverrides
  }
})
