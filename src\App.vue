<script setup lang="ts">
import { RouterView } from 'vue-router'
import { 
  NLoadingBarProvider, 
  NMessageProvider, 
  NDialogProvider,
  NConfigProvider,
  GlobalThemeOverrides
} from 'naive-ui'
import { useThemeStore } from '@/stores/theme'
import { storeToRefs } from 'pinia'

// 引入全局样式
import '@/assets/styles/index.less'

// 主题配置
const themeStore = useThemeStore()
const themeOverrides = themeStore.themeOverrides
</script>

<template>
  <n-config-provider
    :theme-overrides="themeOverrides"
    class="min-h-screen flex flex-col bg-gray-50"
  >
    <n-loading-bar-provider>
      <n-message-provider>
        <n-dialog-provider>
          <RouterView />
        </n-dialog-provider>
      </n-message-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>