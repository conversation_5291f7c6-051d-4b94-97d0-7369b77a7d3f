/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const ArrowDownOutlined: typeof import('@vicons/antd')['ArrowDownOutlined']
  const ArrowUpOutlined: typeof import('@vicons/antd')['ArrowUpOutlined']
  const BellOutlined: typeof import('@vicons/antd')['BellOutlined']
  const BookOutlined: typeof import('@vicons/antd')['BookOutlined']
  const CalendarOutlined: typeof import('@vicons/antd')['CalendarOutlined']
  const CheckCircleFilled: typeof import('@vicons/antd')['CheckCircleFilled']
  const CheckCircleOutlined: typeof import('@vicons/antd')['CheckCircleOutlined']
  const ClockCircleOutlined: typeof import('@vicons/antd')['ClockCircleOutlined']
  const CloseCircleOutlined: typeof import('@vicons/antd')['CloseCircleOutlined']
  const CodeOutlined: typeof import('@vicons/antd')['CodeOutlined']
  const DashboardOutlined: typeof import('@vicons/antd')['DashboardOutlined']
  const DeleteOutlined: typeof import('@vicons/antd')['DeleteOutlined']
  const DeploymentUnitOutlined: typeof import('@vicons/antd')['DeploymentUnitOutlined']
  const EditOutlined: typeof import('@vicons/antd')['EditOutlined']
  const EffectScope: typeof import('vue')['EffectScope']
  const ExclamationCircleFilled: typeof import('@vicons/antd')['ExclamationCircleFilled']
  const ExclamationCircleOutlined: typeof import('@vicons/antd')['ExclamationCircleOutlined']
  const ExperimentOutlined: typeof import('@vicons/antd')['ExperimentOutlined']
  const FileTextOutlined: typeof import('@vicons/antd')['FileTextOutlined']
  const FullscreenExitOutlined: typeof import('@vicons/antd')['FullscreenExitOutlined']
  const FullscreenOutlined: typeof import('@vicons/antd')['FullscreenOutlined']
  const HomeOutlined: typeof import('@vicons/antd')['HomeOutlined']
  const LogoutOutlined: typeof import('@vicons/antd')['LogoutOutlined']
  const MessageOutlined: typeof import('@vicons/antd')['MessageOutlined']
  const OrderedListOutlined: typeof import('@vicons/antd')['OrderedListOutlined']
  const PlusOutlined: typeof import('@vicons/antd')['PlusOutlined']
  const ProjectOutlined: typeof import('@vicons/antd')['ProjectOutlined']
  const RightOutlined: typeof import('@vicons/antd')['RightOutlined']
  const RobotOutlined: typeof import('@vicons/antd')['RobotOutlined']
  const RocketOutlined: typeof import('@vicons/antd')['RocketOutlined']
  const SafetyOutlined: typeof import('@vicons/antd')['SafetyOutlined']
  const ScheduleOutlined: typeof import('@vicons/antd')['ScheduleOutlined']
  const SearchOutlined: typeof import('@vicons/antd')['SearchOutlined']
  const SelectOutlined: typeof import('@vicons/antd')['SelectOutlined']
  const SendOutlined: typeof import('@vicons/antd')['SendOutlined']
  const SettingOutlined: typeof import('@vicons/antd')['SettingOutlined']
  const StarFilled: typeof import('@vicons/antd')['StarFilled']
  const StarOutlined: typeof import('@vicons/antd')['StarOutlined']
  const SyncOutlined: typeof import('@vicons/antd')['SyncOutlined']
  const TeamOutlined: typeof import('@vicons/antd')['TeamOutlined']
  const UnorderedListOutlined: typeof import('@vicons/antd')['UnorderedListOutlined']
  const UserOutlined: typeof import('@vicons/antd')['UserOutlined']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const effectScope: typeof import('vue')['effectScope']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDialog: typeof import('naive-ui')['useDialog']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useLoadingBar: typeof import('naive-ui')['useLoadingBar']
  const useMessage: typeof import('naive-ui')['useMessage']
  const useModel: typeof import('vue')['useModel']
  const useNotification: typeof import('naive-ui')['useNotification']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
