<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import TaskView, { type Task } from '@/components/task/TaskView.vue'

const props = defineProps<{
  projectInfo: {
    id: number
    name: string
    [key: string]: any
  }
}>()

const message = useMessage()
const dialog = useDialog()

// 模拟任务数据 - 只显示当前项目的任务
const tasks = ref<Task[]>([
  {
    id: 1,
    name: '开发用户登录功能',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name,
    assigneeName: '张三',
    assigneeAvatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
    priority: 'high',
    status: 'in_progress',
    dueDate: '2024-01-15',
    progress: 75,
    description: '实现用户登录、注册、密码重置等功能',
    tags: ['前端', 'Vue3']
  },
  {
    id: 2,
    name: '设计数据库表结构',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name,
    assigneeName: '李四',
    priority: 'urgent',
    status: 'todo',
    dueDate: '2024-01-10',
    progress: 0,
    description: '设计用户、项目、任务等核心表结构',
    tags: ['数据库', 'MySQL']
  },
  {
    id: 3,
    name: '编写API接口文档',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name,
    assigneeName: '王五',
    priority: 'medium',
    status: 'done',
    dueDate: '2024-01-05',
    progress: 100,
    description: '编写RESTful API接口文档',
    tags: ['文档', 'API']
  },
  {
    id: 4,
    name: '前端组件开发',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name,
    assigneeName: '赵六',
    priority: 'high',
    status: 'in_progress',
    dueDate: '2024-01-20',
    progress: 45,
    description: '开发可复用的前端组件库',
    tags: ['前端', '组件']
  },
  {
    id: 5,
    name: '单元测试编写',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name,
    assigneeName: '孙七',
    priority: 'low',
    status: 'todo',
    dueDate: '2024-01-25',
    progress: 0,
    description: '为核心功能编写单元测试',
    tags: ['测试', 'Jest']
  }
])

const loading = ref(false)

// 事件处理函数
const handleCreate = () => {
  message.info('创建新任务')
  // TODO: 打开创建任务弹窗或跳转到创建页面
}

const handleEdit = (task: Task) => {
  message.info(`编辑任务: ${task.name}`)
  // TODO: 打开编辑任务弹窗或跳转到编辑页面
}

const handleDelete = (task: Task) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除任务"${task.name}"吗？此操作不可恢复。`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      // 从任务列表中移除
      const index = tasks.value.findIndex(t => t.id === task.id)
      if (index > -1) {
        tasks.value.splice(index, 1)
        message.success('任务删除成功')
      }
    }
  })
}

const handleView = (task: Task) => {
  message.info(`查看任务: ${task.name}`)
  // TODO: 跳转到任务详情页面
}

// 获取任务数据
const fetchData = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取项目任务列表
    // 这里使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    console.error('获取任务列表失败:', error)
    message.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<template>
  <div class="project-task-container p-4">
    <TaskView
      :tasks="tasks"
      :loading="loading"
      :show-project-filter="false"
      :project-id="projectInfo.id"
      :title="`${projectInfo.name} - 任务列表`"
      view-mode="table"
      :editable="true"
      @create="handleCreate"
      @edit="handleEdit"
      @delete="handleDelete"
      @view="handleView"
    />
  </div>
</template>

<style scoped>
.project-task-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
