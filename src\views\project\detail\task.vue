<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import TaskView, { type Task } from '@/components/task/TaskView.vue'

const props = defineProps<{
  projectInfo: {
    id: number
    name: string
    [key: string]: any
  }
}>()

const message = useMessage()
const dialog = useDialog()

// 模拟任务数据 - 只显示当前项目的任务
const tasks = ref<Task[]>([
  {
    id: 1,
    title: '开发用户登录功能',
    issueType: 'feature',
    assignee: '张三',
    assigneeAvatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
    reporter: '产品经理',
    description: '实现用户登录、注册、密码重置等功能',
    status: 'in_progress',
    plannedStartTime: '2024-01-01',
    plannedEndTime: '2024-01-15',
    actualStartTime: '2024-01-02',
    duration: 80,
    priority: 'high',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name
  },
  {
    id: 2,
    title: '设计数据库表结构',
    issueType: 'task',
    assignee: '李四',
    reporter: '技术负责人',
    description: '设计用户、项目、任务等核心表结构',
    status: 'todo',
    plannedStartTime: '2024-01-05',
    plannedEndTime: '2024-01-10',
    duration: 40,
    priority: 'urgent',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name
  },
  {
    id: 3,
    title: '编写API接口文档',
    issueType: 'task',
    assignee: '王五',
    reporter: '技术负责人',
    description: '编写RESTful API接口文档',
    status: 'done',
    plannedStartTime: '2024-01-01',
    plannedEndTime: '2024-01-05',
    actualStartTime: '2024-01-01',
    actualEndTime: '2024-01-04',
    duration: 32,
    priority: 'medium',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name
  },
  {
    id: 4,
    title: '前端组件开发',
    issueType: 'feature',
    assignee: '赵六',
    reporter: '前端负责人',
    description: '开发可复用的前端组件库',
    status: 'in_progress',
    plannedStartTime: '2024-01-10',
    plannedEndTime: '2024-01-20',
    actualStartTime: '2024-01-12',
    duration: 60,
    priority: 'high',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name
  },
  {
    id: 5,
    title: '单元测试编写',
    issueType: 'task',
    assignee: '孙七',
    reporter: '测试负责人',
    description: '为核心功能编写单元测试',
    status: 'todo',
    plannedStartTime: '2024-01-20',
    plannedEndTime: '2024-01-25',
    duration: 24,
    priority: 'low',
    projectId: props.projectInfo.id,
    projectName: props.projectInfo.name
  }
])

const loading = ref(false)

// 事件处理函数
const handleCreate = () => {
  message.info('创建新任务')
  // TODO: 打开创建任务弹窗或跳转到创建页面
}

const handleEdit = (task: Task) => {
  message.info(`编辑任务: ${task.title}`)
  // TODO: 打开编辑任务弹窗或跳转到编辑页面
}

const handleDelete = (task: Task) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除任务"${task.title}"吗？此操作不可恢复。`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      // 从任务列表中移除
      const index = tasks.value.findIndex(t => t.id === task.id)
      if (index > -1) {
        tasks.value.splice(index, 1)
        message.success('任务删除成功')
      }
    }
  })
}

const handleView = (task: Task) => {
  message.info(`查看任务: ${task.title}`)
  // TODO: 跳转到任务详情页面
}

// 获取任务数据
const fetchData = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取项目任务列表
    // 这里使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    console.error('获取任务列表失败:', error)
    message.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<template>
  <div class="project-task-container p-4">
    <TaskView
      :tasks="tasks"
      :loading="loading"
      :show-project-filter="false"
      :project-id="projectInfo.id"
      :title="`${projectInfo.name} - 任务列表`"
      view-mode="table"
      :editable="true"
      @create="handleCreate"
      @edit="handleEdit"
      @delete="handleDelete"
      @view="handleView"
    />
  </div>
</template>

<style scoped>
.project-task-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
