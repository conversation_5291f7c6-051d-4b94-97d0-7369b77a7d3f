<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'
import { 
  NDataTable,
  NButton,
  NInput,
  NSelect,
  NGrid,
  NGi,
  NForm,
  NFormItem,
  NPopconfirm,
  NIcon,
  NTag,
  NEmpty,
  useMessage,
  type SelectOption,
  type DataTableColumns
} from 'naive-ui'
import { SearchOutlined, PlusOutlined } from '@vicons/antd'

type StatusType = 'not_started' | 'in_progress' | 'completed' | 'delayed'
type PriorityType = 'low' | 'medium' | 'high' | 'urgent'

interface Task {
  id: number
  name: string
  status: StatusType
  priority: PriorityType
  progress: number
  assignee: string
  description?: string
  dueDate: string
}

const props = defineProps<{
  projectInfo: Record<string, unknown>
  taskList: Task[]
}>()

const message = useMessage()
// 筛选表单数据
const filterForm = ref({
  status: null as StatusType | null,
  priority: null as PriorityType | null,
  keyword: ''
})

// 筛选条件
const filterStatus = ref<string | null>(null)
const filterPriority = ref<string | null>(null)
const searchText = ref('')

// 状态选项 - 用于筛选下拉框
const statusOptions: SelectOption[] = [
  { label: '未开始', value: 'not_started' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已延期', value: 'delayed' }
]

// 优先级选项 - 用于筛选下拉框
const priorityOptions: SelectOption[] = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 状态和优先级映射
const statusConfig = {
  not_started: { text: '未开始', type: 'default' },
  in_progress: { text: '进行中', type: 'primary' },
  completed: { text: '已完成', type: 'success' },
  delayed: { text: '已延期', type: 'error' }
} as const

const priorityConfig = {
  low: { text: '低', type: 'default' },
  medium: { text: '中', type: 'info' },
  high: { text: '高', type: 'warning' },
  urgent: { text: '紧急', type: 'error' }
} as const

// 同步筛选条件到响应式变量
watchEffect(() => {
  filterStatus.value = filterForm.value.status
  filterPriority.value = filterForm.value.priority
  searchText.value = filterForm.value.keyword
})

// 过滤后的任务列表
const filteredTaskList = computed(() => {
  return props.taskList.filter(task => {
    // 状态筛选
    if (filterStatus.value && task.status !== filterStatus.value) return false
    // 优先级筛选
    if (filterPriority.value && task.priority !== filterPriority.value) return false
    // 搜索文本
    if (searchText.value) {
      const search = searchText.value.toLowerCase()
      const description = task.description?.toLowerCase() ?? ''
      const assignee = task.assignee?.toLowerCase() ?? ''
      return (
        task.name.toLowerCase().includes(search) ||
        description.includes(search) ||
        assignee.includes(search)
      )
    }
    return true
  })
})

// 表格列定义
const taskTableColumns: DataTableColumns<Task> = [
  {
    title: '任务名称',
    key: 'name',
    width: 200
  },
  {
    title: '状态',
    key: 'status'
  },
  {
    title: '优先级',
    key: 'priority'
  },
  {
    title: '负责人',
    key: 'assignee'
  },
  {
    title: '进度',
    key: 'progress'
  },
  {
    title: '操作',
    key: 'actions'
  }
]

// 处理编辑
const handleEdit = (row: Task) => {
  void message.info(`编辑任务: ${row.name}`)
  // 这里可以跳转到编辑页面或打开编辑弹窗
}

// 处理删除
const handleDelete = (row: Task) => {
  void message.success(`已删除任务: ${row.name}`)
  // 这里调用删除接口
}

// 分页
const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: computed(() => filteredTaskList.value.length),
  onChange: (page: number) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  },
  prefix: ({ itemCount }: { itemCount: number | undefined }) => {
    return `共 ${itemCount} 条`
  }
})

// 重置筛选条件
// 重置所有筛选条件
const resetFilters = (): void => {
  filterForm.value = {
    status: null,
    priority: null,
    keyword: ''
  }
  pagination.value.page = 1
}
</script>

<template>
  <div class="task-container p-4">

    <n-card :bordered="false">
      <template #header>
        <div class="flex justify-between items-center">
          <h4 class="m-0 text-lg font-medium">任务列表</h4>
          <n-button type="primary">
            <template #icon>
              <n-icon><plus-outlined /></n-icon>
            </template>
            新建任务
          </n-button>
        </div>
      </template>
      
      <n-data-table
        :columns="taskTableColumns"
        :data="filteredTaskList"
        :pagination="pagination"
        :bordered="false"
        :row-key="(row: Task) => row.id"
        class="task-table"
        :loading="false"
        striped
      >
        <template #status="{ row }">
          <n-tag 
            :type="statusConfig[row.status]?.type || 'default'"
            size="small"
          >
            {{ statusConfig[row.status]?.text || row.status }}
          </n-tag>
        </template>

        <template #priority="{ row }">
          <n-tag 
            :type="priorityConfig[row.priority]?.type || 'default'"
            size="small"
          >
            {{ priorityConfig[row.priority]?.text || row.priority }}
          </n-tag>
        </template>

        <template #progress="{ row }">
          <div class="progress-cell">
            <div class="progress-bar">
              <div 
                class="progress-fill"
                :style="{ width: `${row.progress}%` }"
              />
            </div>
            <span class="progress-text">{{ row.progress }}%</span>
          </div>
        </template>

        <template #actions="{ row }">
          <div class="action-buttons">
            <n-button text type="primary" size="small" @click="handleEdit(row)">
              编辑
            </n-button>
            <n-popconfirm
              @positive-click="() => handleDelete(row)"
              positive-text="确认"
              negative-text="取消"
            >
              <template #trigger>
                <n-button text type="error" size="small" style="margin-left: 8px">
                  删除
                </n-button>
              </template>
              确定删除该任务吗？
            </n-popconfirm>
          </div>
        </template>

        <template #empty>
          <n-empty description="暂无任务数据" class="py-8" />
        </template>
      </n-data-table>
    </n-card>
  </div>
</template>

<style scoped>
.task-container {
  @apply flex flex-col h-full;
}

:deep(.n-card) {
  @apply rounded-lg shadow-sm;
}

:deep(.n-card__content) {
  @apply p-0;
}

:deep(.n-data-table) {
  @apply rounded-lg overflow-hidden;
}

:deep(.n-data-table-th) {
  @apply bg-gray-50 font-medium text-gray-500 text-sm;
}

:deep(.n-data-table-td) {
  @apply py-3;
}

:deep(.progress-cell) {
  @apply flex items-center;
}

:deep(.progress-bar) {
  flex: 1;
  height: 0.375rem;
  background-color: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
  margin-right: 0.5rem;
}

:deep(.progress-fill) {
  @apply h-full bg-primary transition-all duration-300;
}

:deep(.progress-text) {
  min-width: 2.5rem;
  text-align: right;
  font-size: 0.75rem;
  line-height: 1rem;
  color: #6b7280;
}

:deep(.action-buttons) {
  @apply flex items-center;
}

.status-tag {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.status-not_started {
  @apply bg-gray-100 text-gray-600;
}

.status-in_progress {
  @apply bg-blue-50 text-blue-600;
}

.status-completed {
  @apply bg-green-50 text-green-600;
}

.status-delayed {
  @apply bg-red-50 text-red-600;
}
</style>
