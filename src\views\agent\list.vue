<template>
  <page-layout title="Agent列表">
    <n-card :bordered="false" class="mb-4">
      <n-data-table
        :columns="columns"
        :data="agentList"
        :pagination="pagination"
        :loading="loading"
        :row-key="(row) => row.id"
        @update:sorter="handleSorterChange"
      />
    </n-card>

    <!-- 创建/编辑弹窗 -->
    <n-modal v-model:show="showModal" :title="modalTitle" preset="dialog">
      <n-form
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item label="Agent名称" path="name">
          <n-input
            v-model:value="formModel.name"
            placeholder="请输入Agent名称"
          />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input
            v-model:value="formModel.description"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5 }"
            placeholder="请输入Agent描述"
          />
        </n-form-item>
        <n-form-item label="状态" path="status">
          <n-switch
            v-model:value="formModel.status"
            :checked-value="1"
            :unchecked-value="0"
          >
            <template #checked>已启用</template>
            <template #unchecked>已禁用</template>
          </n-switch>
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" :loading="submitting" @click="handleSubmit"
            >确定</n-button
          >
        </n-space>
      </template>
    </n-modal>
  </page-layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from "vue";
import { useRouter } from "vue-router";
import { NTag, useMessage, useDialog, NButton } from "naive-ui";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  MessageOutlined,
} from "@vicons/antd";
import type { DataTableColumns } from "naive-ui";
import PageLayout from "@/layouts/PageLayout.vue";

const message = useMessage();
const dialog = useDialog();
const router = useRouter();
const loading = ref(false);
const showModal = ref(false);
const submitting = ref(false);
const formRef = ref();
const modalTitle = ref("新建Agent");

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
    fetchAgentList();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    fetchAgentList();
  },
});

// 表格列配置
const createColumns = (): DataTableColumns => [
  {
    title: "ID",
    key: "id",
    width: 80,
    sorter: "default",
  },
  {
    title: "Agent名称",
    key: "name",
    sorter: "default",
  },
  {
    title: "描述",
    key: "description",
    ellipsis: true,
  },
  {
    title: "状态",
    key: "status",
    width: 100,
    render: (row) => {
      return h(
        NTag,
        {
          type: row.status === 1 ? "success" : "default",
          size: "small",
          round: true,
        },
        { default: () => (row.status === 1 ? "已启用" : "已禁用") }
      );
    },
  },
  {
    title: "创建时间",
    key: "createTime",
    width: 180,
    sorter: "default",
  },
  {
    title: "操作",
    key: "actions",
    width: 200,
    render: (row) => {
      return h("div", { class: "flex gap-2" }, [
        h(
          NButton,
          {
            size: "small",
            type: "primary",
            ghost: true,
            onClick: () => handleEdit(row),
          },
          { default: () => "编辑" }
        ),
        h(
          NButton,
          {
            size: "small",
            type: "primary",
            onClick: () => handleChat(row),
          },
          { icon: () => h(MessageOutlined), default: () => "对话" }
        ),
        h(
          NButton,
          {
            size: "small",
            type: "error",
            ghost: true,
            onClick: () => handleDelete(row),
          },
          { icon: () => h(DeleteOutlined) }
        ),
      ]);
    },
  },
];

const columns = createColumns();

// 表单模型
const formModel = reactive({
  id: undefined as number | undefined,
  name: "",
  description: "",
  status: 1,
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: "请输入Agent名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在2到50个字符之间", trigger: "blur" },
  ],
  description: [{ max: 200, message: "不能超过200个字符", trigger: "blur" }],
};

// Agent列表数据
const agentList = ref<
  Array<{
    id: number;
    name: string;
    description: string;
    status: number;
    createTime: string;
  }>
>([]);

// 获取Agent列表
const fetchAgentList = async () => {
  try {
    loading.value = true;
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500));
    agentList.value = Array.from({ length: 15 }).map((_, index) => ({
      id: index + 1,
      name: `Agent ${index + 1}`,
      description: `这是第 ${index + 1} 个Agent的描述信息`,
      status: index % 3 === 0 ? 0 : 1,
      createTime: new Date(
        Date.now() - index * 24 * 60 * 60 * 1000
      ).toLocaleString(),
    }));
    pagination.itemCount = 15;
  } catch (error) {
    console.error("获取Agent列表失败:", error);
    message.error("获取Agent列表失败");
  } finally {
    loading.value = false;
  }
};

// 处理排序变化
const handleSorterChange = (sorter: any) => {
  console.log("排序变化:", sorter);
  fetchAgentList();
};

// 新建Agent
const handleCreate = () => {
  modalTitle.value = "新建Agent";
  Object.assign(formModel, {
    id: undefined,
    name: "",
    description: "",
    status: 1,
  });
  showModal.value = true;
};

// 编辑Agent
const handleEdit = (row: any) => {
  modalTitle.value = "编辑Agent";
  Object.assign(formModel, row);
  showModal.value = true;
};

// 删除Agent
const handleDelete = (row: any) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除Agent "${row.name}" 吗？此操作不可恢复。`,
    positiveText: "删除",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 500));
        message.success("删除成功");
        fetchAgentList();
      } catch (error) {
        console.error("删除Agent失败:", error);
        message.error("删除失败");
      }
    },
  });
};

// 开始对话
const handleChat = (row: any) => {
  // 跳转到对话页面
  router.push({ name: 'AgentChat', params: { id: row.id.toString() } });
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        submitting.value = true;
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 500));
        message.success(formModel.id ? "更新成功" : "创建成功");
        showModal.value = false;
        fetchAgentList();
      } catch (error) {
        console.error("操作失败:", error);
        message.error(formModel.id ? "更新失败" : "创建失败");
      } finally {
        submitting.value = false;
      }
    }
  });
};

// 初始化加载数据
onMounted(() => {
  fetchAgentList();
});
</script>

<style scoped>
.agent-list {
  padding: 20px;
}

:deep(.n-data-table-th) {
  font-weight: 600;
}

:deep(.n-button) {
  margin-right: 8px;
}

:deep(.n-pagination) {
  margin-top: 20px;
  justify-content: flex-end;
}
</style>
