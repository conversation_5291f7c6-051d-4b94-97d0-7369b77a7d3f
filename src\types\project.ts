// 项目相关类型定义

// 基础枚举类型
export type ProjectStatus = 'not_started' | 'in_progress' | 'finishing' | 'completed' | 'paused' | 'cancelled'
export type ProjectPriority = 'low' | 'medium' | 'high' | 'urgent'
export type ProjectType = 'product' | 'design' | 'data' | 'operation' | 'marketing'

export type TaskStatus = 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'cancelled'
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent'

export type ProcessStatus = 'pending' | 'in_progress' | 'completed' | 'skipped' | 'failed'
export type ProcessType = 'requirement' | 'design' | 'development' | 'testing' | 'deployment' | 'review'

export type DocumentType = 'requirement' | 'design' | 'technical' | 'test' | 'user_manual' | 'api' | 'other'
export type DocumentStatus = 'draft' | 'review' | 'approved' | 'published' | 'archived'

// 项目类
export interface Project {
  id: number | string
  name: string
  description?: string
  cover?: string
  version?: string

  // 基本信息
  manager: string
  type: ProjectType
  priority: ProjectPriority
  status: ProjectStatus

  // 时间信息
  startDate: number | null // 时间戳
  endDate: number | null // 时间戳
  createdAt: number // 时间戳
  updatedAt: number // 时间戳

  // 团队信息
  teamMembers?: string[] // 团队成员ID列表

  // 标签和分类
  tags: string[]
  category?: string

  // 状态标识
  starred: boolean // 是否关注
  archived: boolean // 是否归档

  // 统计信息
  totalTasks?: number // 总任务数
  completedTasks?: number // 已完成任务数
  totalDocuments?: number // 总文档数

  // 扩展字段
  customFields?: Record<string, any>

  // 图片错误状态（前端使用）
  imageError?: boolean

  // 计算属性（只读）
  readonly progress?: number // 基于时间计算的进度百分比 0-100
}

// 项目流程类
export interface ProjectProcess {
  id: number | string
  projectId: number | string
  
  // 基本信息
  name: string
  description?: string
  type: ProcessType
  status: ProcessStatus
  
  // 顺序和依赖
  order: number // 流程顺序
  dependencies?: string[] // 依赖的流程ID列表
  
  // 时间信息
  plannedStartDate?: number // 计划开始时间
  plannedEndDate?: number // 计划结束时间
  actualStartDate?: number // 实际开始时间
  actualEndDate?: number // 实际结束时间
  estimatedDuration?: number // 预估工期（天）
  actualDuration?: number // 实际工期（天）
  
  // 负责人和参与者
  assignee?: string // 负责人ID
  participants?: string[] // 参与者ID列表
  reviewer?: string // 审核人ID
  
  // 进度和质量
  progress: number // 0-100
  quality?: number // 质量评分 0-100
  
  // 交付物
  deliverables?: string[] // 交付物列表
  artifacts?: string[] // 产出物文件ID列表
  
  // 审核信息
  reviewComments?: string // 审核意见
  reviewDate?: number // 审核时间
  
  // 状态变更记录
  statusHistory?: Array<{
    status: ProcessStatus
    timestamp: number
    operator: string
    comment?: string
  }>
  
  // 扩展字段
  customFields?: Record<string, any>
  
  createdAt: number
  updatedAt: number
}

// 项目文档类
export interface ProjectDocument {
  id: number | string
  projectId: number | string
  taskId?: number | string // 关联的任务ID
  processId?: number | string // 关联的流程ID
  
  // 基本信息
  title: string
  content?: string // 文档内容
  summary?: string // 文档摘要
  type: DocumentType
  status: DocumentStatus
  
  // 文件信息
  fileName?: string // 文件名
  fileSize?: number // 文件大小（字节）
  fileType?: string // 文件类型 (pdf, doc, md, etc.)
  fileUrl?: string // 文件下载链接
  
  // 版本控制
  version: string // 版本号
  versionHistory?: Array<{
    version: string
    timestamp: number
    author: string
    changes: string
  }>
  
  // 作者和权限
  author: string // 作者ID
  editors?: string[] // 编辑者ID列表
  viewers?: string[] // 查看者ID列表
  isPublic: boolean // 是否公开
  
  // 审核流程
  reviewer?: string // 审核人ID
  reviewComments?: string // 审核意见
  reviewDate?: number // 审核时间
  approver?: string // 批准人ID
  approvalDate?: number // 批准时间
  
  // 分类和标签
  category?: string
  tags: string[]
  keywords?: string[] // 关键词，用于搜索
  
  // 关联关系
  relatedDocuments?: string[] // 相关文档ID列表
  references?: string[] // 引用的外部资源
  
  // 统计信息
  viewCount?: number // 查看次数
  downloadCount?: number // 下载次数
  
  // 时间信息
  publishDate?: number // 发布时间
  expiryDate?: number // 过期时间
  
  // 扩展字段
  customFields?: Record<string, any>
  
  createdAt: number
  updatedAt: number
}

// 项目统计信息
export interface ProjectStats {
  projectId: number | string
  
  // 任务统计
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  delayedTasks: number
  
  // 流程统计
  totalProcesses: number
  completedProcesses: number
  inProgressProcesses: number
  
  // 文档统计
  totalDocuments: number
  publishedDocuments: number
  draftDocuments: number
  
  // 团队统计
  totalMembers: number
  activeMembers: number
  
  // 时间统计
  totalEstimatedHours: number
  totalActualHours: number
  averageTaskCompletionTime: number // 平均任务完成时间（天）
  
  // 质量统计
  averageQualityScore: number
  onTimeCompletionRate: number // 按时完成率
  
  // 更新时间
  lastUpdated: number
}

// API 响应类型
export interface ProjectListResponse {
  data: Project[]
  total: number
  page: number
  pageSize: number
}