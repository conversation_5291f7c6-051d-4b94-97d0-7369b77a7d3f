@import './variables.less';

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #app {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: @text-color;
  background-color: @bg-color;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

// 通用工具类
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pointer {
  cursor: pointer;
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity @transition-time @ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
