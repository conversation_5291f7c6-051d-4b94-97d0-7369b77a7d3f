<script setup lang="ts">
import { ref, h, watch } from 'vue'
import type { Component } from 'vue'
import { useRouter, useRoute, RouterLink } from 'vue-router'
import { NIcon, NLayout, NLayoutSider, NLayoutHeader, NLayoutContent, NMenu, NAvatar, NDropdown, useMessage } from 'naive-ui'
import { HomeOutlined, DashboardOutlined, ProjectOutlined, TeamOutlined, SettingOutlined, BellOutlined, SearchOutlined, UserOutlined, LogoutOutlined, FileTextOutlined, RobotOutlined, OrderedListOutlined } from '@vicons/antd'

const router = useRouter()
const route = useRoute()
const message = useMessage()

const collapsed = ref(false)
const activeKey = ref<string | number>('project')

interface MenuOption {
  label: string
  key: string
  icon?: () => any
  path?: string
  children?: MenuOption[]
}

interface SubMenuItem {
  label: string
  key: string
  path: string
}

interface SubMenuGroup {
  title: string
  key: string
  children: SubMenuItem[]
}

// 主菜单配置
const menuOptions: MenuOption[] = [
  {
    label: '项目中心',
    key: 'project',
    icon: () => h(ProjectOutlined),
    path: '/project/overview'
  },
  {
    label: '任务管理',
    key: 'task',
    icon: () => h(OrderedListOutlined),
    path: '/task/list'
  },
  {
    label: 'AI助手',
    key: 'agent',
    icon: () => h(RobotOutlined),
    path: '/agent/list'
  },
  {
    label: '系统设置',
    key: 'system',
    icon: () => h(SettingOutlined),
    path: '/system'
  }
]

// 子菜单配置
const subMenuOptions = ref<SubMenuGroup[]>([
  {
    title: '项目',
    key: 'project',
    children: [
      { label: '项目概览', key: 'project-overview', path: '/project/overview' },
    ]
  },
  {
    title: '任务',
    key: 'task',
    children: [
      { label: '任务列表', key: 'task-list', path: '/task/list' },
    ]
  },
  {
    title: '系统',
    key: 'system',
    children: [
      { label: '基本设置', key: 'basic-settings', path: '/system' }
    ]
  },
  {
    title: 'AI助手',
    key: 'agent',
    children: [
      { label: 'AI助手列表', key: 'agent-list', path: '/agent/list' },
      { label: 'AI对话', key: 'agent-chat', path: '/agent/chat/1' }
    ]
  }
])

// 当前激活的子菜单
const activeSubMenu = ref('')

// 根据路由更新激活的菜单项
const updateActiveMenu = () => {
  const path = route.path

  // 更新主菜单激活项
  const mainMenu = menuOptions.find(item => path.startsWith(`/${item.key}`))
  if (mainMenu) {
    activeKey.value = mainMenu.key
  }

  // 查找并激活匹配的子菜单
  for (const group of subMenuOptions.value) {
    const matchedItem = group.children.find(item => {
      const basePath = item.path.split('/').slice(0, 3).join('/')
      return path.startsWith(basePath)
    })
    if (matchedItem) {
      activeSubMenu.value = group.key
      break
    }
  }

  // 更新子菜单激活项
  for (const group of subMenuOptions.value) {
    const subMenu = group.children.find(item => item.path && path.startsWith(item.path.split('#')[0]))
    if (subMenu) {
      activeSubMenu.value = subMenu.key
      break
    }
  }
}

// 监听路由变化
watch(() => route.path, () => {
  updateActiveMenu()
}, { immediate: true })

// 处理主菜单点击
const handleMenuSelect = (key: string) => {
  const menu = menuOptions.find(item => item.key === key)
  if (menu) {
    router.push(menu.path)
  }
}

// 处理子菜单点击
const handleSubMenuSelect = (key: string, item: any) => {
  if (item.path) {
    router.push(item.path)
  }
}

const renderIcon = (icon: Component) => {
  return () => h(NIcon, null, { default: () => h(icon) })
}

const userMenuOptions = [
  {
    label: '个人中心',
    key: 'profile',
    icon: renderIcon(UserOutlined)
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: renderIcon(LogoutOutlined)
  }
]

const handleUserMenuSelect = (key: string) => {
  if (key === 'logout') {
    message.info('退出登录')
    // 这里添加退出登录逻辑
  } else if (key === 'profile') {
    // 跳转到个人中心
  }
}
</script>

<template>
  <div class="main-layout">
    <n-layout has-sider class="layout-container">
      <!-- 一级侧边栏 -->
      <n-layout-sider
        bordered
        collapse-mode="width"
        :collapsed-width="64"
        :width="64"
        :native-scrollbar="false"
        show-trigger="bar"
        :collapsed="collapsed"
        @collapse="collapsed = true"
        @expand="collapsed = false"
        class="primary-sider">
        <div class="logo-container">
          <div class="logo">PPMS</div>
        </div>
        <n-menu
          v-model:value="activeKey"
          :collapsed="collapsed"
          :collapsed-width="64"
          :collapsed-icon-size="22"
          :options="menuOptions as any"
          @update:value="handleMenuSelect" />
      </n-layout-sider>

      <!-- 二级侧边栏 -->
      <n-layout-sider
        v-if="activeKey && subMenuOptions.some((menu: any) => menu.key === activeKey)"
        bordered
        width="200"
        class="secondary-sider">
        <div v-for="menu in subMenuOptions.filter((menu) => menu.key === activeKey)" :key="menu.key"
          class="submenu-group">
          <div class="submenu-title">{{ menu.title }}</div>
          <n-menu
            v-model:value="activeSubMenu"
            :options="menu.children"
            @update:value="(key, item) => handleSubMenuSelect(key, item)" />
        </div>
      </n-layout-sider>

      <n-layout class="content-layout">
        <n-layout-content class="main-content">
          <router-view v-slot="{ Component, route }">
            <transition name="fade" mode="out-in">
              <component :is="Component" :key="route.path" v-if="Component" />
            </transition>
          </router-view>
        </n-layout-content>
      </n-layout>
    </n-layout>
  </div>
</template>

<style scoped>
.main-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  height: 100vh;
  width: 100%;
}

.primary-sider,
.secondary-sider {
  height: 100vh;
  transition: all 0.3s;
  flex-shrink: 0;
}

.content-layout {
  height: 100vh;
  flex: 1;
  min-width: 0;
}

.main-content {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64px;
  border-bottom: 1px solid var(--n-border-color);
  user-select: none;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #2d8cf0;
  background: linear-gradient(135deg, #2d8cf0 0%, #19be6b 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.submenu-group {
  margin-bottom: 16px;
}

.submenu-title {
  padding: 12px 16px;
  font-size: 13px;
  color: #909399;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  user-select: none;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  z-index: 1;
}

.search-bar {
  width: 300px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-badge {
  margin-right: 8px;
}

.user-avatar {
  cursor: pointer;
  transition: transform 0.2s;
}

.user-avatar:hover {
  transform: scale(1.1);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滚动条样式 */
:deep(.n-scrollbar-rail) {
  z-index: 2;
}

:deep(.n-menu-item-content) {
  border-radius: 6px;
  margin: 0 8px;
  transition: all 0.2s;
}

:deep(.n-menu-item-content--selected) {
  background-color: rgba(45, 140, 240, 0.1);
}

:deep(.n-menu-item-content:hover) {
  background-color: rgba(45, 140, 240, 0.05);
}

:deep(.n-layout-sider) {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

:deep(.n-layout-sider--show-trigger) {
  transition: all 0.2s;
}

:deep(.n-layout-sider--collapsed) {
  width: 64px !important;
}

:deep(.n-layout-sider--collapsed .n-menu-item-content) {
  padding: 0 18px;
}

:deep(.n-layout-sider--collapsed .n-menu-item-content__icon) {
  margin-right: 0;
}

:deep(.n-layout-sider--collapsed .n-menu-item-content__arrow) {
  display: none;
}

:deep(.n-layout-sider--collapsed .n-menu-item-content .n-menu-item-content-header) {
  opacity: 0;
  width: 0;
  padding: 0;
  overflow: hidden;
}
</style>