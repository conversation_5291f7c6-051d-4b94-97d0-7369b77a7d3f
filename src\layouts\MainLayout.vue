<script setup lang="ts">
import { ref, h, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NIcon, NLayout, NLayoutSider, NLayoutContent, NInput, NButton } from 'naive-ui'
import { ProjectOutlined, SettingOutlined, RobotOutlined, OrderedListOutlined } from '@vicons/antd'

const router = useRouter()
const route = useRoute()

const activeKey = ref<string | number>('project')

interface MenuOption {
  label: string
  key: string
  icon?: () => any
  path?: string
  children?: MenuOption[]
}

interface SubMenuItem {
  label: string
  key: string
  path: string
}

interface SubMenuGroup {
  title: string
  key: string
  children: SubMenuItem[]
}

// 主菜单配置
const menuOptions: MenuOption[] = [
  {
    label: '项目',
    key: 'project',
    icon: () => h(ProjectOutlined),
    path: '/project/overview'
  },
  {
    label: '任务',
    key: 'task',
    icon: () => h(OrderedListOutlined),
    path: '/task/list'
  },
  {
    label: 'Agent',
    key: 'agent',
    icon: () => h(RobotOutlined),
    path: '/agent/list'
  },
  {
    label: '设置',
    key: 'system',
    icon: () => h(SettingOutlined),
    path: '/system'
  }
]

// 子菜单配置
const subMenuOptions = ref<SubMenuGroup[]>([
  {
    title: '项目',
    key: 'project',
    children: [
      { label: '项目概览', key: 'project-overview', path: '/project/overview' },
    ]
  },
  {
    title: '任务',
    key: 'task',
    children: [
      { label: '任务列表', key: 'task-list', path: '/task/list' },
    ]
  },
  {
    title: '系统',
    key: 'system',
    children: [
      { label: '基本设置', key: 'basic-settings', path: '/system' }
    ]
  },
  {
    title: 'AI',
    key: 'agent',
    children: [
      { label: 'AI助手列表', key: 'agent-list', path: '/agent/list' },
    ]
  }
])

// 当前激活的子菜单
const activeSubMenu = ref('')

// 根据路由更新激活的菜单项
const updateActiveMenu = () => {
  const path = route.path

  // 更新主菜单激活项
  const mainMenu = menuOptions.find(item => path.startsWith(`/${item.key}`))
  if (mainMenu) {
    activeKey.value = mainMenu.key
  }

  // 查找并激活匹配的子菜单
  for (const group of subMenuOptions.value) {
    const matchedItem = group.children.find(item => {
      const basePath = item.path.split('/').slice(0, 3).join('/')
      return path.startsWith(basePath)
    })
    if (matchedItem) {
      activeSubMenu.value = group.key
      break
    }
  }

  // 更新子菜单激活项
  for (const group of subMenuOptions.value) {
    const subMenu = group.children.find(item => item.path && path.startsWith(item.path.split('#')[0]))
    if (subMenu) {
      activeSubMenu.value = subMenu.key
      break
    }
  }
}

// 监听路由变化
watch(() => route.path, () => {
  updateActiveMenu()
}, { immediate: true })

// 处理主菜单点击
const handleMenuSelect = (key: string) => {
  const menu = menuOptions.find(item => item.key === key)
  if (menu?.path) {
    router.push(menu.path)
  }
}

// 处理子菜单点击
const handleSubMenuSelect = (_key: string, item: any) => {
  if (item.path) {
    router.push(item.path)
  }
}


</script>

<template>
  <div class="main-layout">
    <n-layout has-sider class="layout-container">
      <!-- 一级侧边栏 -->
      <n-layout-sider
        :collapsed-width="80"
        :width="80"
        :native-scrollbar="false"
        :collapsed="false"
        class="primary-sider">
        <div class="primary-menu">
          <div
            v-for="menu in menuOptions"
            :key="menu.key"
            class="primary-menu-item"
            :class="{ active: activeKey === menu.key }"
            @click="handleMenuSelect(menu.key)"
          >
            <div class="menu-icon">
              <component :is="menu.icon" />
            </div>
            <div class="menu-label">{{ menu.label }}</div>
          </div>
        </div>
      </n-layout-sider>

      <!-- 二级侧边栏 -->
      <n-layout-sider
        v-if="activeKey && subMenuOptions.some((menu: any) => menu.key === activeKey)"
        width="280"
        class="secondary-sider">
        <div class="secondary-menu">
          <!-- 页面标题和基础功能 -->
          <div class="secondary-header">
            <div class="page-title">
              {{ subMenuOptions.find(menu => menu.key === activeKey)?.title }}
            </div>
          </div>

          <!-- 菜单内容 -->
          <div class="menu-content">
            <div v-for="menu in subMenuOptions.filter((menu) => menu.key === activeKey)" :key="menu.key">
              <!-- 我的项目区域 -->
              <div class="menu-section">
                <div class="menu-items">
                  <div
                    v-for="item in menu.children"
                    :key="item.key"
                    class="menu-item"
                    :class="{ active: activeSubMenu === item.key }"
                    @click="handleSubMenuSelect(item.key, item)"
                  >
                    <n-icon class="item-icon" color="#52c41a">
                      <component :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z' }))" />
                    </n-icon>
                    <span class="item-label">{{ item.label }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-layout-sider>

      <n-layout class="content-layout">
        <n-layout-content class="main-content">
          <router-view v-slot="{ Component, route }">
            <keep-alive>
              <component :is="Component" :key="route.path" v-if="Component" />
            </keep-alive>
          </router-view>
        </n-layout-content>
      </n-layout>
    </n-layout>
  </div>
</template>

<style scoped>
.main-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  height: 100vh;
  width: 100%;
}

.primary-sider {
  height: 100vh;
  background: #f8f9fa !important;
  border-right: 1px solid #e9ecef !important;
  flex-shrink: 0;
}

.secondary-sider {
  height: 100vh;
  background: #ffffff !important;
  border-right: 1px solid #e9ecef !important;
  flex-shrink: 0;
}

.content-layout {
  height: 100vh;
  flex: 1;
  min-width: 0;
}

.main-content {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 一级菜单样式 */
.primary-menu {
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.primary-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.2s ease;
  width: 56px;
  position: relative;
}

.primary-menu-item:hover {
  background: rgba(24, 144, 255, 0.08);
}

.primary-menu-item.active {
  background: rgba(24, 144, 255, 0.12);
}

.primary-menu-item.active::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 24px;
  background: #1890ff;
  border-radius: 2px;
}

.menu-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  color: #1890ff;
  font-size: 16px;
}

.primary-menu-item.active .menu-icon {
  background: #1890ff;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.menu-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.primary-menu-item.active .menu-label {
  color: #1890ff;
  font-weight: 600;
}

/* 二级菜单样式 */
.secondary-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.secondary-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.beta-tag {
  font-size: 11px;
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 400;
}

.upgrade-btn {
  margin-top: 8px;
}

.search-container {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.menu-content {
  flex: 1;
  padding: 16px 24px;
  overflow-y: auto;
}

.menu-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.section-header .section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.section-icon {
  font-size: 16px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.menu-item:hover {
  background: #f5f5f5;
}

.menu-item.active {
  background: #e6f7ff;
  color: #1890ff;
}

.item-icon {
  font-size: 14px;
}

.item-label {
  font-size: 14px;
  color: #595959;
}

.menu-item.active .item-label {
  color: #1890ff;
  font-weight: 500;
}

.reminder-item {
  font-size: 13px;
  color: #8c8c8c;
  margin-left: 24px;
  padding: 8px 0;
}

.task-items {
  margin-left: 24px;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 2px;
  font-size: 13px;
  color: #595959;
}

.task-item:hover {
  background: #f5f5f5;
}

.task-icon {
  font-size: 12px;
  color: #bfbfbf;
}

.add-task {
  margin-left: 24px;
  margin-top: 12px;
}



/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滚动条样式 */
:deep(.n-scrollbar-rail) {
  z-index: 2;
}

/* 覆盖 Naive UI 默认样式 */
:deep(.n-layout-sider) {
  box-shadow: none !important;
}

:deep(.n-layout-sider .n-layout-sider-scroll-container) {
  overflow: visible !important;
}

/* 输入框样式调整 */
:deep(.search-container .n-input) {
  border-radius: 8px;
}

:deep(.search-container .n-input .n-input__input-el) {
  font-size: 14px;
}

/* 按钮样式调整 */
:deep(.upgrade-btn .n-button) {
  border-radius: 6px;
  font-size: 12px;
  height: 28px;
}

:deep(.add-task .n-button) {
  color: #1890ff;
  font-size: 13px;
}
</style>