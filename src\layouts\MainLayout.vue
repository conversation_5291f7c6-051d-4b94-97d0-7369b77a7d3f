<script setup lang="ts">
import { ref, h, watch } from 'vue'
import type { Component } from 'vue'
import { useRouter, useRoute, RouterLink } from 'vue-router'
import { NIcon, NLayout, NLayoutSider, NLayoutHeader, NLayoutContent, NMenu, NAvatar, NDropdown, useMessage } from 'naive-ui'
import { HomeOutlined, DashboardOutlined, ProjectOutlined, TeamOutlined, SettingOutlined, BellOutlined, SearchOutlined, UserOutlined, LogoutOutlined, FileTextOutlined, RobotOutlined, OrderedListOutlined } from '@vicons/antd'

const router = useRouter()
const route = useRoute()
const message = useMessage()

const collapsed = ref(false)
const activeKey = ref<string | number>('project')

interface MenuOption {
  label: string
  key: string
  icon?: () => any
  path?: string
  children?: MenuOption[]
}

interface SubMenuItem {
  label: string
  key: string
  path: string
}

interface SubMenuGroup {
  title: string
  key: string
  children: SubMenuItem[]
}

// 主菜单配置
const menuOptions: MenuOption[] = [
  {
    label: '项目中心',
    key: 'project',
    icon: () => h(ProjectOutlined),
    path: '/project/overview'
  },
  {
    label: '任务管理',
    key: 'task',
    icon: () => h(OrderedListOutlined),
    path: '/task/list'
  },
  {
    label: 'AI助手',
    key: 'agent',
    icon: () => h(RobotOutlined),
    path: '/agent/list'
  },
  {
    label: '系统设置',
    key: 'system',
    icon: () => h(SettingOutlined),
    path: '/system'
  }
]

// 子菜单配置
const subMenuOptions = ref<SubMenuGroup[]>([
  {
    title: '项目',
    key: 'project',
    children: [
      { label: '项目概览', key: 'project-overview', path: '/project/overview' },
    ]
  },
  {
    title: '任务',
    key: 'task',
    children: [
      { label: '任务列表', key: 'task-list', path: '/task/list' },
    ]
  },
  {
    title: '系统',
    key: 'system',
    children: [
      { label: '基本设置', key: 'basic-settings', path: '/system' }
    ]
  },
  {
    title: 'AI助手',
    key: 'agent',
    children: [
      { label: 'AI助手列表', key: 'agent-list', path: '/agent/list' },
      { label: 'AI对话', key: 'agent-chat', path: '/agent/chat/1' }
    ]
  }
])

// 当前激活的子菜单
const activeSubMenu = ref('')

// 根据路由更新激活的菜单项
const updateActiveMenu = () => {
  const path = route.path

  // 更新主菜单激活项
  const mainMenu = menuOptions.find(item => path.startsWith(`/${item.key}`))
  if (mainMenu) {
    activeKey.value = mainMenu.key
  }

  // 查找并激活匹配的子菜单
  for (const group of subMenuOptions.value) {
    const matchedItem = group.children.find(item => {
      const basePath = item.path.split('/').slice(0, 3).join('/')
      return path.startsWith(basePath)
    })
    if (matchedItem) {
      activeSubMenu.value = group.key
      break
    }
  }

  // 更新子菜单激活项
  for (const group of subMenuOptions.value) {
    const subMenu = group.children.find(item => item.path && path.startsWith(item.path.split('#')[0]))
    if (subMenu) {
      activeSubMenu.value = subMenu.key
      break
    }
  }
}

// 监听路由变化
watch(() => route.path, () => {
  updateActiveMenu()
}, { immediate: true })

// 处理主菜单点击
const handleMenuSelect = (key: string) => {
  const menu = menuOptions.find(item => item.key === key)
  if (menu) {
    router.push(menu.path)
  }
}

// 处理子菜单点击
const handleSubMenuSelect = (key: string, item: any) => {
  if (item.path) {
    router.push(item.path)
  }
}

const renderIcon = (icon: Component) => {
  return () => h(NIcon, null, { default: () => h(icon) })
}

const userMenuOptions = [
  {
    label: '个人中心',
    key: 'profile',
    icon: renderIcon(UserOutlined)
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: renderIcon(LogoutOutlined)
  }
]

const handleUserMenuSelect = (key: string) => {
  if (key === 'logout') {
    message.info('退出登录')
    // 这里添加退出登录逻辑
  } else if (key === 'profile') {
    // 跳转到个人中心
  }
}

// 获取当前菜单标题
const getCurrentMenuTitle = () => {
  const currentMenu = menuOptions.find(item => item.key === activeKey.value)
  return currentMenu?.label || '项目'
}
</script>

<template>
  <div class="main-layout">
    <n-layout has-sider class="layout-container">
      <!-- 一级侧边栏 -->
      <n-layout-sider
        bordered
        collapse-mode="width"
        :collapsed-width="80"
        :width="80"
        :native-scrollbar="false"
        :collapsed="false"
        class="primary-sider">
        <div class="primary-menu">
          <div
            v-for="menu in menuOptions"
            :key="menu.key"
            class="primary-menu-item"
            :class="{ active: activeKey === menu.key }"
            @click="handleMenuSelect(menu.key)"
          >
            <div class="menu-icon">
              <component :is="menu.icon" />
            </div>
            <div class="menu-label">{{ menu.label }}</div>
          </div>
        </div>
      </n-layout-sider>

      <!-- 二级侧边栏 -->
      <n-layout-sider
        v-if="activeKey && subMenuOptions.some((menu: any) => menu.key === activeKey)"
        bordered
        width="280"
        class="secondary-sider">
        <div class="secondary-header">
          <div class="header-title">
            <span class="title-text">{{ getCurrentMenuTitle() }}</span>
            <span class="title-badge">基础版</span>
          </div>
          <div class="upgrade-section">
            <div class="upgrade-btn">
              <n-icon size="16" color="#4A90E2">
                <IconAntDesignRocketOutlined />
              </n-icon>
              <span>立即升级</span>
            </div>
            <div class="upgrade-avatar">
              <n-avatar size="small" src="https://avatars.githubusercontent.com/u/1?v=4" />
            </div>
          </div>
          <div class="search-box">
            <n-input placeholder="搜索 (Ctrl + S)" size="small">
              <template #prefix>
                <n-icon size="14" color="#999">
                  <IconAntDesignSearchOutlined />
                </n-icon>
              </template>
            </n-input>
          </div>
        </div>

        <div class="secondary-content">
          <div v-for="menu in subMenuOptions.filter((menu) => menu.key === activeKey)" :key="menu.key">
            <!-- 我的项目区域 -->
            <div class="menu-section">
              <div class="section-header">
                <n-icon size="16" color="#4A90E2">
                  <IconAntDesignFolderOutlined />
                </n-icon>
                <span class="section-title">我的项目</span>
              </div>
            </div>

            <!-- 我的任务区域 -->
            <div class="menu-section">
              <div class="section-header">
                <n-icon size="16" color="#4A90E2">
                  <IconAntDesignCheckCircleOutlined />
                </n-icon>
                <span class="section-title">我的任务</span>
              </div>
            </div>

            <!-- 捷径区域 -->
            <div class="menu-section">
              <div class="section-header">
                <span class="section-title">捷径</span>
                <n-button text size="small" class="add-btn">
                  <template #icon>
                    <n-icon size="14">
                      <IconAntDesignPlusOutlined />
                    </n-icon>
                  </template>
                </n-button>
              </div>
              <div class="section-content">
                <div class="shortcut-item">我的项目</div>
                <div class="shortcut-item">
                  <n-icon size="14" color="#999" style="margin-right: 8px;">
                    <IconAntDesignNumberOutlined />
                  </n-icon>
                  我最新派给他人的任务
                </div>
                <div class="shortcut-item">
                  <n-icon size="14" color="#999" style="margin-right: 8px;">
                    <IconAntDesignNumberOutlined />
                  </n-icon>
                  我创建的任务
                </div>
                <div class="shortcut-item">
                  <n-icon size="14" color="#999" style="margin-right: 8px;">
                    <IconAntDesignNumberOutlined />
                  </n-icon>
                  我参与的任务
                </div>
                <div class="add-shortcut">
                  <n-icon size="14" color="#4A90E2" style="margin-right: 8px;">
                    <IconAntDesignPlusOutlined />
                  </n-icon>
                  <span>添加捷径</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-layout-sider>

      <n-layout class="content-layout">
        <n-layout-content class="main-content">
          <router-view v-slot="{ Component, route }">
            <transition name="fade" mode="out-in">
              <component :is="Component" :key="route.path" v-if="Component" />
            </transition>
          </router-view>
        </n-layout-content>
      </n-layout>
    </n-layout>
  </div>
</template>

<style scoped>
.main-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  height: 100vh;
  width: 100%;
}

.primary-sider {
  height: 100vh;
  background: #f8f9fa !important;
  border-right: 1px solid #e8eaed !important;
  flex-shrink: 0;
}

.secondary-sider {
  height: 100vh;
  background: #ffffff !important;
  border-right: 1px solid #e8eaed !important;
  flex-shrink: 0;
}

.content-layout {
  height: 100vh;
  flex: 1;
  min-width: 0;
}

.main-content {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 一级菜单样式 */
.primary-menu {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.primary-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  margin: 0 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #5f6368;

  &:hover {
    background-color: #f1f3f4;
    color: #1a73e8;
  }

  &.active {
    background-color: #e8f0fe;
    color: #1a73e8;

    .menu-icon {
      color: #1a73e8;
    }
  }
}

.menu-icon {
  font-size: 20px;
  margin-bottom: 4px;
  transition: color 0.2s ease;
}

.menu-label {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

/* 二级菜单样式 */
.secondary-header {
  padding: 20px 16px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #202124;
}

.title-badge {
  background: #e8f0fe;
  color: #1a73e8;
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.upgrade-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.upgrade-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f8f9fa;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  span {
    font-size: 12px;
    color: #4A90E2;
    font-weight: 500;
  }

  &:hover {
    background: #e8f0fe;
  }
}

.upgrade-avatar {
  .n-avatar {
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
}

.search-box {
  .n-input {
    background: #f8f9fa;
    border: 1px solid #e8eaed;
    border-radius: 8px;

    &:focus-within {
      border-color: #1a73e8;
      background: #fff;
    }
  }
}

.secondary-content {
  padding: 16px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.menu-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e8f0fe;
  }
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #202124;
  margin-left: 8px;
}

.add-btn {
  opacity: 0;
  transition: opacity 0.2s ease;

  .section-header:hover & {
    opacity: 1;
  }
}

.section-content {
  padding-left: 12px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 2px 0;
  border-radius: 6px;
  font-size: 13px;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f1f3f4;
    color: #202124;
  }
}

.add-shortcut {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 2px 0;
  border-radius: 6px;
  font-size: 13px;
  color: #1a73e8;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e8f0fe;
  }

  span {
    color: #1a73e8;
    font-weight: 500;
  }
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滚动条样式 */
:deep(.n-scrollbar-rail) {
  z-index: 2;
}

/* 移除默认的 layout-sider 样式 */
:deep(.n-layout-sider .n-layout-sider-scroll-container) {
  padding: 0;
}

:deep(.n-layout-sider) {
  box-shadow: none;
}
</style>