<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import type { Project } from '@/types'
import { createProjectWithProgress } from '@/utils/project'

interface Props {
  show: boolean
  editData?: Project | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success', project: Project): void
}

const props = withDefaults(defineProps<Props>(), {
  editData: null
})

const emit = defineEmits<Emits>()
const message = useMessage()

// 表单数据
const formRef = ref()
const formData = reactive<Partial<Project>>({
  name: '',
  description: '',
  manager: '',
  type: undefined,
  priority: 'medium',
  status: 'not_started',
  startDate: null,
  endDate: null,
  tags: [],
  cover: '',
  starred: false,
  archived: false
})

// 项目类型选项
const projectTypes = [
  { label: '产品研发', value: 'product' },
  { label: '设计', value: 'design' },
  { label: '数据', value: 'data' },
  { label: '运营', value: 'operation' },
  { label: '市场', value: 'marketing' }
]

// 优先级选项
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'not_started' },
  { label: '进行中', value: 'in_progress' },
  { label: '收尾中', value: 'finishing' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' },
  { label: '已取消', value: 'cancelled' }
]

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入项目名称',
    trigger: ['input', 'blur']
  },
  manager: {
    required: true,
    message: '请输入项目经理',
    trigger: ['input', 'blur']
  },
  type: {
    required: true,
    message: '请选择项目类型',
    trigger: ['change', 'blur']
  }
}

const loading = ref(false)
const isEdit = ref(false)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    manager: '',
    type: undefined,
    priority: 'medium',
    status: 'not_started',
    startDate: null,
    endDate: null,
    tags: [],
    cover: '',
    starred: false,
    archived: false
  })
  nextTick(() => {
    formRef.value?.restoreValidation()
  })
}

// 监听编辑数据变化
watch(() => props.editData, (newData) => {
  if (newData) {
    isEdit.value = true
    Object.assign(formData, {
      id: newData.id,
      name: newData.name,
      description: newData.description,
      manager: newData.manager,
      type: newData.type,
      priority: newData.priority,
      status: newData.status,
      startDate: newData.startDate,
      endDate: newData.endDate,
      tags: [...(newData.tags || [])],
      cover: newData.cover,
      starred: newData.starred,
      archived: newData.archived
    })
  } else {
    isEdit.value = false
    resetForm()
  }
}, { immediate: true })

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 创建项目对象
    const projectData = {
      ...formData,
      id: isEdit.value ? formData.id : Date.now(),
      createdAt: isEdit.value ? (props.editData?.createdAt || Date.now()) : Date.now(),
      updatedAt: Date.now()
    } as Omit<Project, 'progress'>
    
    const project = createProjectWithProgress(projectData)
    
    message.success(isEdit.value ? '项目更新成功！' : '项目创建成功！')
    emit('success', project)
    handleClose()
  } catch (error) {
    message.error('请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:show', false)
  resetForm()
}

// 计算标题
const modalTitle = computed(() => {
  return isEdit.value ? '编辑项目' : '新建项目'
})
</script>

<template>
  <n-modal
    :show="show"
    :mask-closable="false"
    preset="card"
    :title="modalTitle"
    class="project-form-modal"
    style="width: 800px"
    @update:show="emit('update:show', $event)"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="2" :x-gap="24">
        <n-gi>
          <n-form-item label="项目名称" path="name">
            <n-input
              v-model:value="formData.name"
              placeholder="请输入项目名称"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="项目经理" path="manager">
            <n-input
              v-model:value="formData.manager"
              placeholder="请输入项目经理"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="项目类型" path="type">
            <n-select
              v-model:value="formData.type"
              :options="projectTypes"
              placeholder="请选择项目类型"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="优先级">
            <n-select
              v-model:value="formData.priority"
              :options="priorityOptions"
              placeholder="请选择优先级"
            />
          </n-form-item>
        </n-gi>
        <n-gi v-if="isEdit">
          <n-form-item label="项目状态">
            <n-select
              v-model:value="formData.status"
              :options="statusOptions"
              placeholder="请选择项目状态"
            />
          </n-form-item>
        </n-gi>
        <n-gi v-if="!isEdit">
          <n-form-item label="版本号">
            <n-input
              v-model:value="formData.version"
              placeholder="请输入版本号"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="开始日期">
            <n-date-picker
              v-model:value="formData.startDate"
              type="date"
              placeholder="请选择开始日期"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="结束日期">
            <n-date-picker
              v-model:value="formData.endDate"
              type="date"
              placeholder="请选择结束日期"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="项目标签">
            <n-dynamic-tags v-model:value="formData.tags" />
          </n-form-item>
        </n-gi>
        <n-gi v-if="isEdit">
          <n-form-item label="关注状态">
            <n-switch v-model:value="formData.starred">
              <template #checked>已关注</template>
              <template #unchecked>未关注</template>
            </n-switch>
          </n-form-item>
        </n-gi>
        <n-gi :span="2">
          <n-form-item label="项目描述">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              placeholder="请输入项目描述"
              :rows="4"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi :span="2">
          <n-form-item label="项目封面">
            <n-input
              v-model:value="formData.cover"
              placeholder="请输入封面图片URL"
              clearable
            />
          </n-form-item>
        </n-gi>
      </n-grid>
    </n-form>

    <template #footer>
      <div style="display: flex; justify-content: flex-end; gap: 12px;">
        <n-button @click="handleClose">取消</n-button>
        <n-button @click="resetForm">重置</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '保存更改' : '创建项目' }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<style lang="less" scoped>
.project-form-modal {
  :deep(.n-card) {
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  :deep(.n-card__content) {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }

  :deep(.n-card__footer) {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
  }

  :deep(.n-form-item-label) {
    font-weight: 500;
    color: #333;
  }

  :deep(.n-input),
  :deep(.n-select),
  :deep(.n-date-picker) {
    border-radius: 8px;
  }

  :deep(.n-button) {
    border-radius: 8px;
    font-weight: 500;
  }
}
</style>
