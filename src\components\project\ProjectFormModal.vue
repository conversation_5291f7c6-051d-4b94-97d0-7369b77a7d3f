<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { Project } from '@/types'

interface Props {
  show: boolean
  mode: 'create' | 'edit'
  projectData?: Partial<Project>
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success', data: Partial<Project>): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  mode: 'create',
  projectData: () => ({})
})

const emit = defineEmits<Emits>()

const message = useMessage()

// 表单数据
const formRef = ref()
const formData = reactive<Partial<Project>>({
  name: '',
  description: '',
  manager: '',
  type: undefined,
  priority: 'medium',
  startDate: null,
  endDate: null,
  tags: [],
  cover: '',
  status: 'not_started',
  progress: 0
})

// 项目类型选项
const projectTypes = [
  { label: '产品研发', value: 'product' },
  { label: '设计', value: 'design' },
  { label: '数据', value: 'data' },
  { label: '运营', value: 'operation' },
  { label: '市场', value: 'marketing' }
]

// 优先级选项
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'not_started' },
  { label: '进行中', value: 'in_progress' },
  { label: '收尾中', value: 'finishing' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' },
  { label: '已取消', value: 'cancelled' }
]

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入项目名称',
    trigger: ['input', 'blur']
  },
  manager: {
    required: true,
    message: '请输入项目经理',
    trigger: ['input', 'blur']
  },
  type: {
    required: true,
    message: '请选择项目类型',
    trigger: ['change', 'blur']
  },
  startDate: {
    required: true,
    message: '请选择开始日期',
    trigger: ['change', 'blur']
  },
  endDate: {
    required: true,
    message: '请选择结束日期',
    trigger: ['change', 'blur']
  }
}

const loading = ref(false)

// 计算属性
const modalTitle = computed(() => props.mode === 'create' ? '新建项目' : '编辑项目')
const submitText = computed(() => props.mode === 'create' ? '创建项目' : '保存更改')

// 监听显示状态变化
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 监听项目数据变化
watch(() => props.projectData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, newData)
  }
}, { immediate: true, deep: true })

// 监听模态框显示状态
watch(() => props.show, (show) => {
  if (show && props.mode === 'create') {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formRef.value?.restoreValidation()
  Object.assign(formData, {
    name: '',
    description: '',
    manager: '',
    type: undefined,
    priority: 'medium',
    startDate: null,
    endDate: null,
    tags: [],
    cover: '',
    status: 'not_started',
    progress: 0
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const successMessage = props.mode === 'create' ? '项目创建成功！' : '项目更新成功！'
    message.success(successMessage)
    
    emit('success', { ...formData })
    emit('update:show', false)
  } catch (error) {
    message.error('请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:show', false)
}
</script>

<template>
  <n-modal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="modalTitle"
    class="project-form-modal"
    style="width: 800px"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="2" :x-gap="24">
        <n-gi>
          <n-form-item label="项目名称" path="name">
            <n-input
              v-model:value="formData.name"
              placeholder="请输入项目名称"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="项目经理" path="manager">
            <n-input
              v-model:value="formData.manager"
              placeholder="请输入项目经理"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="项目类型" path="type">
            <n-select
              v-model:value="formData.type"
              :options="projectTypes"
              placeholder="请选择项目类型"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="优先级" path="priority">
            <n-select
              v-model:value="formData.priority"
              :options="priorityOptions"
              placeholder="请选择优先级"
            />
          </n-form-item>
        </n-gi>
        <n-gi v-if="mode === 'edit'">
          <n-form-item label="项目状态">
            <n-select
              v-model:value="formData.status"
              :options="statusOptions"
              placeholder="请选择项目状态"
            />
          </n-form-item>
        </n-gi>
        <n-gi v-if="mode === 'edit'">
          <n-form-item label="完成进度">
            <n-slider
              v-model:value="formData.progress"
              :step="5"
              :tooltip="false"
            />
            <div style="text-align: center; margin-top: 8px; font-size: 14px; color: #666;">
              {{ formData.progress }}%
            </div>
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="开始日期" path="startDate">
            <n-date-picker
              v-model:value="formData.startDate"
              type="date"
              placeholder="请选择开始日期"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="结束日期" path="endDate">
            <n-date-picker
              v-model:value="formData.endDate"
              type="date"
              placeholder="请选择结束日期"
              style="width: 100%"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="项目标签">
            <n-dynamic-tags v-model:value="formData.tags" />
          </n-form-item>
        </n-gi>
        <n-gi :span="2">
          <n-form-item label="项目描述">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              placeholder="请输入项目描述"
              :rows="4"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi :span="2">
          <n-form-item label="项目封面">
            <n-input
              v-model:value="formData.cover"
              placeholder="请输入封面图片URL"
              clearable
            />
          </n-form-item>
        </n-gi>
      </n-grid>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button v-if="mode === 'create'" @click="resetForm">重置</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          {{ submitText }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<style lang="less" scoped>
:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-modal) {
  max-height: 90vh;
}

:deep(.n-dialog__content) {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
