<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'

// 定义任务类型
export interface Task {
  id: number
  name: string
  projectId?: number
  projectName?: string
  assigneeName: string
  assigneeAvatar?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'todo' | 'in_progress' | 'done' | 'cancelled'
  dueDate: string
  progress: number
  description?: string
  tags?: string[]
}

// 定义项目类型
export interface Project {
  id: number
  name: string
  color?: string
}

interface Props {
  tasks: Task[]
  projects?: Project[]
  loading?: boolean
  showProjectFilter?: boolean
  projectId?: number
  title?: string
}

interface Emits {
  (e: 'create'): void
  (e: 'edit', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'view', task: Task): void
  (e: 'update:projectId', projectId: number | undefined): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showProjectFilter: false,
  title: '任务列表'
})

const emit = defineEmits<Emits>()
const message = useMessage()

// 筛选条件
const filters = ref({
  projectId: props.projectId,
  status: null as string | null,
  priority: null as string | null,
  assignee: null as string | null,
  keyword: ''
})

// 状态选项
const statusOptions = [
  { label: '全部状态', value: null },
  { label: '未开始', value: 'todo' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'done' },
  { label: '已取消', value: 'cancelled' }
]

// 优先级选项
const priorityOptions = [
  { label: '全部优先级', value: null },
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 项目选项
const projectOptions = computed(() => {
  if (!props.projects) return []
  return [
    { label: '全部项目', value: null },
    ...props.projects.map(project => ({
      label: project.name,
      value: project.id
    }))
  ]
})

// 负责人选项
const assigneeOptions = computed(() => {
  const assignees = [...new Set(props.tasks.map(task => task.assigneeName))]
  return [
    { label: '全部负责人', value: null },
    ...assignees.map(assignee => ({
      label: assignee,
      value: assignee
    }))
  ]
})

// 过滤后的任务列表
const filteredTasks = computed(() => {
  return props.tasks.filter(task => {
    // 项目筛选
    if (filters.value.projectId && task.projectId !== filters.value.projectId) return false
    // 状态筛选
    if (filters.value.status && task.status !== filters.value.status) return false
    // 优先级筛选
    if (filters.value.priority && task.priority !== filters.value.priority) return false
    // 负责人筛选
    if (filters.value.assignee && task.assigneeName !== filters.value.assignee) return false
    // 关键词搜索
    if (filters.value.keyword) {
      const keyword = filters.value.keyword.toLowerCase()
      return task.name.toLowerCase().includes(keyword) ||
             task.description?.toLowerCase().includes(keyword) ||
             task.assigneeName.toLowerCase().includes(keyword)
    }
    return true
  })
})

// 监听项目ID变化
watch(() => filters.value.projectId, (newProjectId) => {
  emit('update:projectId', newProjectId)
})

// 监听props.projectId变化
watch(() => props.projectId, (newProjectId) => {
  if (!props.showProjectFilter) {
    filters.value.projectId = newProjectId
  }
}, { immediate: true })

// 重置筛选条件
const resetFilters = () => {
  filters.value = {
    projectId: props.showProjectFilter ? null : props.projectId,
    status: null,
    priority: null,
    assignee: null,
    keyword: ''
  }
}

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: computed(() => filteredTasks.value.length),
  onChange: (page: number) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  }
})

// 当前页的任务
const currentPageTasks = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredTasks.value.slice(start, end)
})

// 获取状态显示信息
const getStatusInfo = (status: string) => {
  const statusMap = {
    todo: { text: '未开始', type: 'default' },
    in_progress: { text: '进行中', type: 'primary' },
    done: { text: '已完成', type: 'success' },
    cancelled: { text: '已取消', type: 'error' }
  }
  return statusMap[status] || { text: status, type: 'default' }
}

// 获取优先级显示信息
const getPriorityInfo = (priority: string) => {
  const priorityMap = {
    low: { text: '低', type: 'info' },
    medium: { text: '中', type: 'success' },
    high: { text: '高', type: 'warning' },
    urgent: { text: '紧急', type: 'error' }
  }
  return priorityMap[priority] || { text: priority, type: 'default' }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 事件处理
const handleCreate = () => emit('create')
const handleEdit = (task: Task) => emit('edit', task)
const handleDelete = (task: Task) => emit('delete', task)
const handleView = (task: Task) => emit('view', task)
</script>

<template>
  <div class="task-view">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <n-card :bordered="false" size="small">
        <div class="filter-content">
          <div class="filter-row">
            <div class="filter-group">
              <n-input
                v-model:value="filters.keyword"
                placeholder="搜索任务..."
                clearable
                class="search-input"
              >
                <template #prefix>
                  <n-icon>
                    <component :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'm19.6 21l-6.3-6.3q-.75.6-1.725.95Q10.6 16 9.5 16q-2.725 0-4.612-1.887Q3 12.225 3 9.5q0-2.725 1.888-4.613Q6.775 3 9.5 3t4.613 1.887Q16 6.775 16 9.5q0 1.1-.35 2.075q-.35.975-.95 1.725l6.3 6.3ZM9.5 14q1.875 0 3.188-1.312Q14 11.375 14 9.5q0-1.875-1.312-3.188Q11.375 5 9.5 5Q7.625 5 6.312 6.312Q5 7.625 5 9.5q0 1.875 1.312 3.188Q7.625 14 9.5 14Z' }))" />
                  </n-icon>
                </template>
              </n-input>
            </div>
            
            <div class="filter-group">
              <n-select
                v-if="showProjectFilter"
                v-model:value="filters.projectId"
                :options="projectOptions"
                placeholder="选择项目"
                clearable
                class="filter-select"
              />
              
              <n-select
                v-model:value="filters.status"
                :options="statusOptions"
                placeholder="状态"
                clearable
                class="filter-select"
              />
              
              <n-select
                v-model:value="filters.priority"
                :options="priorityOptions"
                placeholder="优先级"
                clearable
                class="filter-select"
              />
              
              <n-select
                v-model:value="filters.assignee"
                :options="assigneeOptions"
                placeholder="负责人"
                clearable
                class="filter-select"
              />
              
              <n-button @click="resetFilters" class="reset-btn">
                重置
              </n-button>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <n-card :bordered="false">
        <template #header>
          <div class="list-header">
            <h3 class="list-title">{{ title }}</h3>
            <n-button type="primary" @click="handleCreate">
              <template #icon>
                <n-icon>
                  <component :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z' }))" />
                </n-icon>
              </template>
              新建任务
            </n-button>
          </div>
        </template>

        <div class="task-list">
          <div v-if="filteredTasks.length === 0" class="empty-state">
            <n-empty description="暂无任务" />
          </div>
          
          <div v-else class="task-items">
            <div
              v-for="task in currentPageTasks"
              :key="task.id"
              class="task-item"
              @click="handleView(task)"
            >
              <div class="task-content">
                <div class="task-header">
                  <div class="task-name">{{ task.name }}</div>
                  <div class="task-actions">
                    <n-button size="small" text @click.stop="handleEdit(task)">
                      编辑
                    </n-button>
                    <n-button size="small" text type="error" @click.stop="handleDelete(task)">
                      删除
                    </n-button>
                  </div>
                </div>
                
                <div class="task-meta">
                  <div class="task-info">
                    <div class="assignee">
                      <n-avatar
                        :size="24"
                        :src="task.assigneeAvatar"
                        class="assignee-avatar"
                      >
                        {{ task.assigneeName.charAt(0) }}
                      </n-avatar>
                      <span class="assignee-name">{{ task.assigneeName }}</span>
                    </div>
                    
                    <div v-if="showProjectFilter && task.projectName" class="project">
                      <span class="project-name">{{ task.projectName }}</span>
                    </div>
                  </div>
                  
                  <div class="task-status">
                    <n-tag
                      :type="getStatusInfo(task.status).type"
                      size="small"
                      class="status-tag"
                    >
                      {{ getStatusInfo(task.status).text }}
                    </n-tag>
                    
                    <n-tag
                      :type="getPriorityInfo(task.priority).type"
                      size="small"
                      class="priority-tag"
                    >
                      {{ getPriorityInfo(task.priority).text }}
                    </n-tag>
                  </div>
                </div>
                
                <div class="task-progress">
                  <div class="progress-info">
                    <span class="progress-text">{{ task.progress }}%</span>
                    <span class="due-date">{{ formatDate(task.dueDate) }}</span>
                  </div>
                  <n-progress
                    :percentage="task.progress"
                    :show-indicator="false"
                    :height="4"
                    class="progress-bar"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="filteredTasks.length > 0" class="pagination-wrapper">
            <n-pagination
              v-model:page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :item-count="pagination.itemCount"
              :page-sizes="pagination.pageSizes"
              show-size-picker
              show-quick-jumper
              :prefix="({ itemCount }) => `共 ${itemCount} 条`"
            />
          </div>
        </div>
      </n-card>
    </div>
  </div>
</template>

<style lang="less" scoped>
.task-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.filter-section {
  flex-shrink: 0;
}

.filter-content {
  .filter-row {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .filter-group {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .search-input {
    width: 300px;
  }
  
  .filter-select {
    width: 140px;
  }
  
  .reset-btn {
    margin-left: 8px;
  }
}

.task-list-section {
  flex: 1;
  min-height: 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.task-list {
  height: 100%;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
}

.task-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  .task-item:hover & {
    opacity: 1;
  }
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  display: flex;
  gap: 16px;
  align-items: center;
}

.assignee {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-name {
  font-size: 13px;
  color: #595959;
}

.project-name {
  font-size: 13px;
  color: #8c8c8c;
}

.task-status {
  display: flex;
  gap: 8px;
}

.task-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 12px;
  color: #595959;
  font-weight: 500;
}

.due-date {
  font-size: 12px;
  color: #8c8c8c;
}

:deep(.n-card) {
  border-radius: 8px;
}

:deep(.n-card__content) {
  padding: 16px;
}

:deep(.n-progress-rail) {
  background-color: #f5f5f5;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 24px 0 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}
</style>
