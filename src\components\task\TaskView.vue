<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useMessage } from 'naive-ui'
import TaskListView from './views/TaskListView.vue'
import TaskTableView from './views/TaskTableView.vue'
import TaskKanbanView from './views/TaskKanbanView.vue'

// 定义任务类型
export interface Task {
  id: number // 序号
  title: string // 标题
  issueType: 'bug' | 'feature' | 'task' | 'improvement' | 'story' // 问题类型
  assignee: string // 指派人
  assigneeAvatar?: string // 指派人头像
  reporter: string // 报告人
  reporterAvatar?: string // 报告人头像
  description?: string // 描述
  status: 'todo' | 'in_progress' | 'done' | 'cancelled' | 'blocked' // 状态
  plannedStartTime?: string // 计划开始时间
  plannedEndTime?: string // 计划结束时间
  actualStartTime?: string // 实际开始时间
  actualEndTime?: string // 实际结束时间
  duration?: number // 时长(小时)
  priority: 'low' | 'medium' | 'high' | 'urgent' // 优先级
  projectId?: number
  projectName?: string
}

// 定义项目类型
export interface Project {
  id: number
  name: string
  color?: string
}

interface Props {
  tasks: Task[]
  projects?: Project[]
  loading?: boolean
  showProjectFilter?: boolean
  projectId?: number
  title?: string
  viewMode?: 'list' | 'table' | 'kanban'
  editable?: boolean
}

interface Emits {
  (e: 'create'): void
  (e: 'edit', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'view', task: Task): void
  (e: 'update:projectId', projectId: number | undefined): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showProjectFilter: false,
  title: '任务列表',
  viewMode: 'list',
  editable: false
})

const emit = defineEmits<Emits>()
const message = useMessage()

// 筛选条件
const filters = ref({
  projectId: props.projectId,
  status: null as string | null,
  priority: null as string | null,
  assignee: null as string | null,
  keyword: ''
})

// 状态选项
const statusOptions = [
  { label: '全部状态', value: null },
  { label: '未开始', value: 'todo' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'done' },
  { label: '已取消', value: 'cancelled' }
]

// 优先级选项
const priorityOptions = [
  { label: '全部优先级', value: null },
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 项目选项
const projectOptions = computed(() => {
  if (!props.projects) return []
  return [
    { label: '全部项目', value: null },
    ...props.projects.map(project => ({
      label: project.name,
      value: project.id
    }))
  ]
})

// 负责人选项
const assigneeOptions = computed(() => {
  const assignees = [...new Set(props.tasks.map(task => task.assignee))]
  return [
    { label: '全部负责人', value: null },
    ...assignees.map(assignee => ({
      label: assignee,
      value: assignee
    }))
  ]
})

// 过滤后的任务列表
const filteredTasks = computed(() => {
  return props.tasks.filter(task => {
    // 项目筛选
    if (filters.value.projectId && task.projectId !== filters.value.projectId) return false
    // 状态筛选
    if (filters.value.status && task.status !== filters.value.status) return false
    // 优先级筛选
    if (filters.value.priority && task.priority !== filters.value.priority) return false
    // 负责人筛选
    if (filters.value.assignee && task.assignee !== filters.value.assignee) return false
    // 关键词搜索
    if (filters.value.keyword) {
      const keyword = filters.value.keyword.toLowerCase()
      return task.title.toLowerCase().includes(keyword) ||
             task.description?.toLowerCase().includes(keyword) ||
             task.assignee.toLowerCase().includes(keyword)
    }
    return true
  })
})

// 监听项目ID变化
watch(() => filters.value.projectId, (newProjectId) => {
  emit('update:projectId', newProjectId)
})

// 监听props.projectId变化
watch(() => props.projectId, (newProjectId) => {
  if (!props.showProjectFilter) {
    filters.value.projectId = newProjectId
  }
}, { immediate: true })

// 重置筛选条件
const resetFilters = () => {
  filters.value = {
    projectId: props.showProjectFilter ? null : props.projectId,
    status: null,
    priority: null,
    assignee: null,
    keyword: ''
  }
}

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: computed(() => filteredTasks.value.length),
  onChange: (page: number) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  }
})

// 当前页的任务
const currentPageTasks = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredTasks.value.slice(start, end)
})

// 视图模式
const currentViewMode = ref(props.viewMode)

// 是否显示二级筛选
const showSecondaryFilters = ref(false)


// 字段更新处理
const handleFieldUpdate = (task: Task, field: keyof Task, value: any) => {
  if (props.editable) {
    const updatedTask = { ...task, [field]: value }
    emit('edit', updatedTask)
  }
}

// 视图模式切换
const handleViewModeChange = (mode: 'list' | 'table' | 'kanban') => {
  currentViewMode.value = mode
}

// 状态变更处理
const handleStatusChange = (task: Task, newStatus: string) => {
  const updatedTask = { ...task, status: newStatus as Task['status'] }
  emit('edit', updatedTask)
}

// 事件处理
const handleCreate = () => emit('create')
const handleEdit = (task: Task) => emit('edit', task)
const handleDelete = (task: Task) => emit('delete', task)
const handleView = (task: Task) => emit('view', task)
</script>

<template>
  <div class="task-view">
    <!-- 顶部筛选和操作区域 -->
    <div class="top-section">
      <n-card :bordered="false" size="small">
        <div class="top-content">
          <!-- 左侧：项目筛选 -->
          <div class="left-filters">
            <n-select
              v-if="showProjectFilter"
              v-model:value="filters.projectId"
              :options="projectOptions"
              placeholder="所有任务"
              clearable
              class="project-select"
              style="width: 200px"
            />
            <span v-else class="project-name">{{ title }}</span>
          </div>

          <!-- 右侧：搜索、视图、操作按钮 -->
          <div class="right-actions">
            <!-- 搜索框 -->
            <n-input
              v-model:value="filters.keyword"
              placeholder="搜索 (Ctrl + S)"
              clearable
              class="search-input"
              style="width: 240px"
            >
              <template #prefix>
                <n-icon>
                  <component :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'm19.6 21l-6.3-6.3q-.75.6-1.725.95Q10.6 16 9.5 16q-2.725 0-4.612-1.887Q3 12.225 3 9.5q0-2.725 1.888-4.613Q6.775 3 9.5 3t4.613 1.887Q16 6.775 16 9.5q0 1.1-.35 2.075q-.35.975-.95 1.725l6.3 6.3ZM9.5 14q1.875 0 3.188-1.312Q14 11.375 14 9.5q0-1.875-1.312-3.188Q11.375 5 9.5 5Q7.625 5 6.312 6.312Q5 7.625 5 9.5q0 1.875 1.312 3.188Q7.625 14 9.5 14Z' }))" />
                </n-icon>
              </template>
            </n-input>

            <!-- 视图切换按钮组 -->
            <n-button-group>
              <n-button
                :type="currentViewMode === 'list' ? 'primary' : 'default'"
                @click="handleViewModeChange('list')"
                size="small"
              >
                <template #icon>
                  <n-icon>
                    <component :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z' }))" />
                  </n-icon>
                </template>
                列表视图
              </n-button>
              <n-button
                :type="currentViewMode === 'table' ? 'primary' : 'default'"
                @click="handleViewModeChange('table')"
                size="small"
              >
                <template #icon>
                  <n-icon>
                    <component :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M10 4H4c-1.11 0-2 .89-2 2v3h8V4zm0 5H2v3h8v-3zm0 5H2v3c0 1.11.89 2 2 2h6v-5zm2-10h8c1.11 0 2 .89 2 2v3h-10V4zm0 5h10v3H12v-3zm0 5h10v3c0 1.11-.89 2-2 2h-8v-5z' }))" />
                  </n-icon>
                </template>
                表格视图
              </n-button>
              <n-button
                :type="currentViewMode === 'kanban' ? 'primary' : 'default'"
                @click="handleViewModeChange('kanban')"
                size="small"
              >
                <template #icon>
                  <n-icon>
                    <component :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M4 5h4v14H4V5zm6 0h4v8h-4V5zm6 0h4v11h-4V5z' }))" />
                  </n-icon>
                </template>
                看板视图
              </n-button>
            </n-button-group>

            <!-- 重置按钮 -->
            <n-button @click="resetFilters" size="small">
              重置
            </n-button>

            <!-- 新建任务按钮 -->
            <n-button type="primary" @click="handleCreate" size="small">
              <template #icon>
                <n-icon>
                  <component :is="() => h('svg', { viewBox: '0 0 24 24', fill: 'currentColor' }, h('path', { d: 'M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z' }))" />
                </n-icon>
              </template>
              新建任务
            </n-button>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 二级筛选区域 -->
    <div v-if="showSecondaryFilters" class="secondary-filters">
      <n-card :bordered="false" size="small">
        <div class="secondary-content">
          <div class="filter-group">
            <n-select
              v-model:value="filters.status"
              :options="statusOptions"
              placeholder="状态"
              clearable
              class="filter-select"
            />

            <n-select
              v-model:value="filters.priority"
              :options="priorityOptions"
              placeholder="优先级"
              clearable
              class="filter-select"
            />

            <n-select
              v-model:value="filters.assignee"
              :options="assigneeOptions"
              placeholder="负责人"
              clearable
              class="filter-select"
            />
          </div>
        </div>
      </n-card>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <n-card :bordered="false">

        <!-- 任务内容区域 -->
        <div class="task-content-area">
          <!-- 列表视图 -->
          <TaskListView
            v-if="currentViewMode === 'list'"
            :tasks="currentPageTasks"
            :loading="loading"
            :show-project-filter="showProjectFilter"
            @edit="handleEdit"
            @delete="handleDelete"
            @view="handleView"
          />

          <!-- 表格视图 -->
          <TaskTableView
            v-else-if="currentViewMode === 'table'"
            :tasks="currentPageTasks"
            :loading="loading"
            :editable="editable"
            @edit="handleEdit"
            @delete="handleDelete"
            @view="handleView"
            @field-update="handleFieldUpdate"
          />

          <!-- 看板视图 -->
          <TaskKanbanView
            v-else-if="currentViewMode === 'kanban'"
            :tasks="filteredTasks"
            :loading="loading"
            @edit="handleEdit"
            @delete="handleDelete"
            @view="handleView"
            @status-change="handleStatusChange"
          />

          <!-- 分页 (仅在列表和表格视图显示) -->
          <template v-if="filteredTasks.length > 0 && currentViewMode !== 'kanban'">
            <div class="pagination-wrapper">
              <n-pagination
                v-model:page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :item-count="pagination.itemCount"
                :page-sizes="pagination.pageSizes"
                show-size-picker
                show-quick-jumper
                :prefix="({ itemCount }) => `共 ${itemCount} 条`"
              />
            </div>
          </template>
        </div>
      </n-card>
    </div>
  </div>
</template>

<style lang="less" scoped>
.task-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

/* 顶部区域样式 */
.top-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.left-filters {
  display: flex;
  align-items: center;
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 二级筛选区域 */
.secondary-filters {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.secondary-content {
  padding: 8px 0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-select {
  width: 120px;
}

.project-select {
  min-width: 200px;
}

/* 任务列表区域 */
.task-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}



.task-content-area {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
}

.task-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  .task-item:hover & {
    opacity: 1;
  }
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  display: flex;
  gap: 16px;
  align-items: center;
}

.assignee {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-name {
  font-size: 13px;
  color: #595959;
}

.project-name {
  font-size: 13px;
  color: #8c8c8c;
}

.task-status {
  display: flex;
  gap: 8px;
}

.task-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 12px;
  color: #595959;
  font-weight: 500;
}

.due-date {
  font-size: 12px;
  color: #8c8c8c;
}

:deep(.n-card) {
  border-radius: 8px;
}

:deep(.n-card__content) {
  padding: 16px;
}

:deep(.n-progress-rail) {
  background-color: #f5f5f5;
}



.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 24px 0 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  flex-shrink: 0;
}
</style>
