import { defineConfig, presetUno, presetAttributify, presetIcons } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
  ],
  shortcuts: {
    'flex-center': 'flex justify-center items-center',
    'flex-col-center': 'flex flex-col justify-center items-center',
    'page-container': 'p-4 w-full h-full overflow-auto',
    'card': 'bg-white rounded shadow p-4',
    'btn': 'px-4 py-2 rounded transition-colors',
    'btn-primary': 'bg-blue-500 hover:bg-blue-600 text-white',
    'btn-ghost': 'hover:bg-gray-100',
  },
  theme: {
    colors: {
      primary: 'var(--primary-color)',
      success: 'var(--success-color)',
      warning: 'var(--warning-color)',
      error: 'var(--error-color)',
      info: 'var(--info-color)',
    },
  },
})
