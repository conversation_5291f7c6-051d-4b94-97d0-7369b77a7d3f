<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { useRouter, useRoute, type LocationQueryValue } from 'vue-router'
import { UserOutlined, LockOutlined } from '@vicons/antd'

interface FormValue {
  username: string
  password: string
}

const formRef = ref<FormInst | null>(null)
const message = useMessage()
const router = useRouter()
const route = useRoute()
const loading = ref(false)
const rememberMe = ref(false)
const isMobile = ref(window.innerWidth < 768)

// 响应式处理移动端
const handleResize = () => {
  isMobile.value = window.innerWidth < 768
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  // 检查是否有记住的账号
  const savedUsername = localStorage.getItem('saved_username')
  if (savedUsername) {
    formValue.value.username = savedUsername
    rememberMe.value = true
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})

const formValue = ref<FormValue>({
  username: '',
  password: ''
})

const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { 
      min: 3, 
      max: 20, 
      message: '用户名长度在3-20个字符之间', 
      trigger: 'blur' 
    },
    { 
      pattern: /^[a-zA-Z0-9_]+$/, 
      message: '用户名只能包含字母、数字和下划线', 
      trigger: 'blur' 
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { 
      min: 6, 
      max: 20, 
      message: '密码长度在6-20个字符之间', 
      trigger: 'blur' 
    },
    { 
      validator: (_, value: string) => {
        return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\s]{6,20}$/.test(value)
      },
      message: '密码必须包含大小写字母和数字',
      trigger: 'blur'
    }
  ]
}

// 处理登录
const handleSubmit = async (): Promise<void> => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 保存登录状态
    const token = `mock-jwt-token-${Date.now()}`
    localStorage.setItem('token', token)
    
    // 记住用户名
    if (rememberMe.value) {
      localStorage.setItem('saved_username', formValue.value.username)
    } else {
      localStorage.removeItem('saved_username')
    }
    
    message.success('登录成功')
    
    // 跳转到首页或之前访问的页面
    const redirect = route.query.redirect as string || '/'
    await router.push(redirect)
    
  } catch (errors) {
    const errorMessages = (errors as Error[]).map(e => e.message).join('\n')
    if (errorMessages) {
      message.error(errorMessages)
    }
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="login-container">
    <n-card class="login-card" :bordered="false">
      <template #header>
        <div class="login-header">
          <img src="@/assets/logo.svg" alt="Logo" class="logo" v-if="!isMobile">
          <h2>欢迎登录</h2>
          <p>PPMS 项目管理系统</p>
        </div>
      </template>

      <n-form
        ref="formRef"
        :model="formValue"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="large"
      >
        <n-form-item path="username" label="用户名">
          <n-input 
            v-model:value="formValue.username" 
            placeholder="请输入用户名" 
            :loading="loading"
            :disabled="loading"
          >
            <template #prefix>
              <n-icon :component="UserOutlined" />
            </template>
          </n-input>
        </n-form-item>
        <n-form-item path="password" label="密码">
          <n-input
            v-model:value="formValue.password"
            type="password"
            show-password-on="click"
            placeholder="请输入密码"
            :loading="loading"
            :disabled="loading"
            @keyup.enter="handleSubmit"
          >
            <template #prefix>
              <n-icon :component="LockOutlined" />
            </template>
          </n-input>
        </n-form-item>
        <n-form-item>
          <div class="login-options">
            <n-checkbox v-model:checked="rememberMe">记住我</n-checkbox>
            <n-a href="#" class="forgot-password">忘记密码？</n-a>
          </div>
        </n-form-item>
        <n-form-item>
          <n-button
            type="primary"
            size="large"
            :loading="loading"
            :disabled="loading"
            @click="handleSubmit"
            style="width: 100%"
            :ghost="loading"
          >
            {{ loading ? '登录中...' : '登 录' }}
          </n-button>
        </n-form-item>
      </n-form>
      <div class="login-footer">
        <span>还没有账号？</span>
        <n-a href="#" class="register-link">立即注册</n-a>
      </div>
    </n-card>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
}

.login-header {
  text-align: center;
  padding: 20px 0;
  position: relative;
}

.login-header .logo {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  border-radius: 50%;
  object-fit: contain;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.login-header h2 {
  margin: 0;
  color: #2d8cf0;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.login-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  font-weight: 400;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 8px;
  transition: all 0.3s;
}

:deep(.n-input:focus) {
  border-color: #2d8cf0;
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}

.login-options {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
}

.forgot-password {
  font-size: 14px;
  color: #666;
  transition: color 0.3s;
}

.forgot-password:hover {
  color: #2d8cf0;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  color: #999;
  font-size: 14px;
}
</style>
