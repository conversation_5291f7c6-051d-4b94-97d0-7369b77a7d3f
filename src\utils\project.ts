// 项目相关工具函数

import type { Project, ProjectStatus } from '@/types'

/**
 * 计算项目基于时间的进度百分比
 * @param project 项目对象
 * @returns 进度百分比 (0-100)
 */
export function calculateTimeProgress(project: Project): number {
  // 如果没有开始时间或结束时间，返回0
  if (!project.startDate || !project.endDate) {
    return 0
  }

  const now = Date.now()
  const startTime = project.startDate
  const endTime = project.endDate

  // 如果当前时间在开始时间之前，进度为0
  if (now < startTime) {
    return 0
  }

  // 如果当前时间在结束时间之后，进度为100
  if (now > endTime) {
    return 100
  }

  // 计算时间进度百分比
  const totalDuration = endTime - startTime
  const elapsedTime = now - startTime
  const timeProgress = Math.round((elapsedTime / totalDuration) * 100)

  return Math.max(0, Math.min(100, timeProgress))
}

/**
 * 获取项目的实际进度（包含状态修正）
 * @param project 项目对象
 * @returns 实际进度百分比 (0-100)
 */
export function getProjectProgress(project: Project): number {
  const timeProgress = calculateTimeProgress(project)

  // 根据项目状态调整进度
  switch (project.status) {
    case 'not_started':
      return 0
    case 'completed':
      return 100
    case 'cancelled':
      return 0
    case 'paused':
      // 暂停状态保持当前时间进度，但不超过暂停前的进度
      return timeProgress
    case 'in_progress':
    case 'finishing':
      return timeProgress
    default:
      return timeProgress
  }
}

/**
 * 判断项目是否延期
 * @param project 项目对象
 * @returns 是否延期
 */
export function isProjectDelayed(project: Project): boolean {
  if (!project.endDate || project.status === 'completed') {
    return false
  }

  const now = Date.now()
  return now > project.endDate && project.status !== 'completed'
}

/**
 * 获取项目剩余天数
 * @param project 项目对象
 * @returns 剩余天数，负数表示已延期
 */
export function getProjectRemainingDays(project: Project): number {
  if (!project.endDate) {
    return 0
  }

  const now = Date.now()
  const remainingTime = project.endDate - now
  return Math.ceil(remainingTime / (24 * 60 * 60 * 1000))
}

/**
 * 格式化项目持续时间
 * @param project 项目对象
 * @returns 格式化的持续时间字符串
 */
export function formatProjectDuration(project: Project): string {
  if (!project.startDate || !project.endDate) {
    return '未设置'
  }

  const duration = project.endDate - project.startDate
  const days = Math.ceil(duration / (24 * 60 * 60 * 1000))

  if (days < 30) {
    return `${days}天`
  } else if (days < 365) {
    const months = Math.ceil(days / 30)
    return `${months}个月`
  } else {
    const years = Math.floor(days / 365)
    const remainingMonths = Math.ceil((days % 365) / 30)
    return remainingMonths > 0 ? `${years}年${remainingMonths}个月` : `${years}年`
  }
}

/**
 * 获取项目状态的显示文本
 * @param status 项目状态
 * @returns 状态显示文本
 */
export function getProjectStatusText(status: ProjectStatus): string {
  const statusMap: Record<ProjectStatus, string> = {
    not_started: '未开始',
    in_progress: '进行中',
    finishing: '收尾中',
    completed: '已完成',
    paused: '已暂停',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/**
 * 获取项目类型的显示文本
 * @param type 项目类型
 * @returns 类型显示文本
 */
export function getProjectTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    product: '产品研发',
    design: '设计',
    data: '数据',
    operation: '运营',
    marketing: '市场'
  }
  return typeMap[type] || type
}

/**
 * 获取项目优先级的显示文本
 * @param priority 优先级
 * @returns 优先级显示文本
 */
export function getProjectPriorityText(priority: string): string {
  const priorityMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

/**
 * 创建带有计算进度的项目对象
 * @param projectData 原始项目数据
 * @returns 包含计算进度的项目对象
 */
export function createProjectWithProgress(projectData: Omit<Project, 'progress'>): Project {
  const project = projectData as Project
  
  // 使用 Object.defineProperty 定义只读的 progress 属性
  Object.defineProperty(project, 'progress', {
    get() {
      return getProjectProgress(this)
    },
    enumerable: true,
    configurable: false
  })

  return project
}
