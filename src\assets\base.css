/* 基础颜色变量 */
:root {
  --color-white: #ffffff;
  --color-white-soft: #f8f8f8;
  --color-white-mute: #f2f2f2;
  --color-indigo: #2c3e50;
  --color-divider-light-1: rgba(60, 60, 60, 0.29);
  --color-divider-light-2: rgba(60, 60, 60, 0.12);
  --color-text-light-1: #2c3e50;
  --color-text-light-2: rgba(60, 60, 60, 0.66);
  
  /* 语义化颜色变量 */
  --color-background: var(--color-white);
  --color-background-soft: var(--color-white-soft);
  --color-background-mute: var(--color-white-mute);
  --color-border: var(--color-divider-light-2);
  --color-border-hover: var(--color-divider-light-1);
  --color-heading: var(--color-text-light-1);
  --color-text: var(--color-text-light-1);
  --section-gap: 160px;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
