<script setup lang="ts">
import { ref } from "vue";
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NSelect,
  useMessage,
} from "naive-ui";
import PageLayout from "@/layouts/PageLayout.vue";

const message = useMessage();

// 基础设置
interface FormState {
  siteName: string;
  siteUrl: string;
  adminEmail: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
}

const form = ref<FormState>({
  siteName: "PPMS 项目管理系统",
  siteUrl: "https://ppms.example.com",
  adminEmail: "<EMAIL>",
  timezone: "Asia/Shanghai",
  dateFormat: "YYYY-MM-DD",
  timeFormat: "HH:mm:ss",
});

// 保存设置
const saveSettings = () => {
  message.success("基本设置已保存");
};
</script>

<template>
  <page-layout title="系统设置">
    <n-card :bordered="false" class="mb-4">
      <n-form
        :model="form"
        label-placement="left"
        label-width="auto"
        class="settings-form"
      >
        <n-form-item label="站点名称">
          <n-input v-model:value="form.siteName" placeholder="请输入站点名称" />
        </n-form-item>
        <n-form-item label="站点URL">
          <n-input v-model:value="form.siteUrl" placeholder="请输入站点URL" />
        </n-form-item>
        <n-form-item label="管理员邮箱">
          <n-input
            v-model:value="form.adminEmail"
            placeholder="请输入管理员邮箱"
          />
        </n-form-item>
        <n-form-item label="时区">
          <n-select
            v-model:value="form.timezone"
            :options="[
              { label: '上海 (UTC+8)', value: 'Asia/Shanghai' },
              { label: '东京 (UTC+9)', value: 'Asia/Tokyo' },
              { label: '伦敦 (UTC+0)', value: 'Europe/London' },
              { label: '纽约 (UTC-5)', value: 'America/New_York' },
            ]"
          />
        </n-form-item>
        <n-form-item label="日期格式">
          <n-select
            v-model:value="form.dateFormat"
            :options="[
              { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' },
              { label: 'MM/DD/YYYY', value: 'MM/DD/YYYY' },
              { label: 'DD/MM/YYYY', value: 'DD/MM/YYYY' },
            ]"
          />
        </n-form-item>
        <n-form-item>
          <n-button type="primary" @click="saveSettings">保存设置</n-button>
        </n-form-item>
      </n-form>
    </n-card>
  </page-layout>
</template>

<style scoped>
.system-settings {
  padding: 16px;
}

.system-settings h2 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2d3d;
}

.settings-form {
  max-width: 600px;
  margin-top: 16px;
}

.form-help {
  margin-left: 8px;
  color: #8c8c8c;
  font-size: 14px;
}

.about-container {
  max-width: 600px;
  padding: 16px;
}

.about-container h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2d3d;
}

.about-container h4 {
  margin: 24px 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2d3d;
}

.about-container p {
  margin: 8px 0;
  color: #4e5969;
}

.system-info {
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 12px 16px;
}
</style>
